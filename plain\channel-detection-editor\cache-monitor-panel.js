/**
 * 缓存监控面板 - 可视化缓存性能监控和调试工具
 * 
 * === 功能特性 ===
 * - 实时性能监控和统计显示
 * - 缓存命中率和响应时间可视化
 * - 交互式缓存管理操作
 * - 详细的调试信息和错误日志
 * - 缓存数据预览和搜索
 * - 性能优化建议和报告
 * 
 * === 监控指标 ===
 * - 缓存命中率（按模块分类）
 * - 平均响应时间和时间节省
 * - 缓存大小和条目数量
 * - 错误率和健康状态
 * - 实时调用频率统计
 * 
 * === 操作功能 ===
 * - 启用/禁用缓存功能
 * - 清理指定模块缓存
 * - 导出性能报告
 * - 缓存预热操作
 * - 调试模式切换
 * 
 * @MONITORING 缓存性能实时监控
 * @DEBUG 调试工具和日志查看
 * @MANAGEMENT 缓存管理操作界面
 */

class CacheMonitorPanel {
    constructor(cacheManager, integrationAdapter, options = {}) {
        this.cacheManager = cacheManager;
        this.integrationAdapter = integrationAdapter;
        this.options = {
            // 面板配置
            autoRefresh: true,
            refreshInterval: 5000, // 5秒刷新
            
            // 图表配置  
            chartUpdateInterval: 1000,
            maxDataPoints: 50,
            
            // 面板样式
            theme: 'dark',
            position: 'bottom-right',
            
            // 功能开关
            enableCharts: true,
            enableDebugLogs: true,
            enableExport: true,
            
            ...options
        };
        
        // 面板状态
        this.panelVisible = false;
        this.panelElement = null;
        
        // 图表数据
        this.chartData = {
            hitRates: [],
            responseTimes: [],
            cacheSize: [],
            timestamps: []
        };
        
        // 刷新定时器
        this.refreshTimer = null;
        this.chartTimer = null;
        
        // 调试日志
        this.debugLogs = [];
        this.maxLogs = 100;
        
        this.initialize();
    }
    
    /**
     * 初始化监控面板
     * @INIT 创建面板UI和启动监控
     */
    initialize() {
        console.log('📊 初始化缓存监控面板...');
        
        try {
            // 创建面板HTML结构
            this.createPanelHTML();
            
            // 绑定事件处理器
            this.bindEventHandlers();
            
            // 启动数据刷新
            if (this.options.autoRefresh) {
                this.startAutoRefresh();
            }
            
            // 启动图表更新
            if (this.options.enableCharts) {
                this.startChartUpdates();
            }
            
            // 设置调试日志监听
            if (this.options.enableDebugLogs) {
                this.setupDebugLogging();
            }
            
            console.log('✅ 缓存监控面板初始化完成');
            
        } catch (error) {
            console.error('❌ 缓存监控面板初始化失败:', error);
        }
    }
    
    /**
     * 创建面板HTML结构
     * @UI 构建监控面板的用户界面
     */
    createPanelHTML() {
        // 创建面板容器
        this.panelElement = document.createElement('div');
        this.panelElement.id = 'cache-monitor-panel';
        this.panelElement.className = `cache-monitor-panel ${this.options.theme} ${this.options.position}`;
        this.panelElement.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 600px;
            height: 400px;
            background: ${this.options.theme === 'dark' ? '#2d3748' : '#ffffff'};
            border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            display: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            color: ${this.options.theme === 'dark' ? '#ffffff' : '#2d3748'};
            overflow: hidden;
        `;
        
        // 面板HTML内容
        this.panelElement.innerHTML = `
            <div class="panel-header" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                border-bottom: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
            ">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 18px;">📊</span>
                    <h3 style="margin: 0; font-size: 16px; font-weight: 600;">缓存监控面板</h3>
                    <span id="cache-status-indicator" class="status-indicator" style="
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: #10b981;
                        display: inline-block;
                    "></span>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button id="cache-refresh-btn" title="刷新数据" style="
                        background: none;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 4px;
                        padding: 4px 8px;
                        cursor: pointer;
                        color: inherit;
                    ">🔄</button>
                    <button id="cache-settings-btn" title="设置" style="
                        background: none;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 4px;
                        padding: 4px 8px;
                        cursor: pointer;
                        color: inherit;
                    ">⚙️</button>
                    <button id="cache-close-btn" title="关闭" style="
                        background: none;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 4px;
                        padding: 4px 8px;
                        cursor: pointer;
                        color: inherit;
                    ">✕</button>
                </div>
            </div>
            
            <div class="panel-tabs" style="
                display: flex;
                background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                border-bottom: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
            ">
                <button class="tab-button active" data-tab="overview" style="
                    flex: 1;
                    padding: 8px 16px;
                    border: none;
                    background: none;
                    cursor: pointer;
                    color: inherit;
                    border-bottom: 2px solid transparent;
                ">概览</button>
                <button class="tab-button" data-tab="performance" style="
                    flex: 1;
                    padding: 8px 16px;
                    border: none;
                    background: none;
                    cursor: pointer;
                    color: inherit;
                    border-bottom: 2px solid transparent;
                ">性能</button>
                <button class="tab-button" data-tab="management" style="
                    flex: 1;
                    padding: 8px 16px;
                    border: none;
                    background: none;
                    cursor: pointer;
                    color: inherit;
                    border-bottom: 2px solid transparent;
                ">管理</button>
                <button class="tab-button" data-tab="debug" style="
                    flex: 1;
                    padding: 8px 16px;
                    border: none;
                    background: none;
                    cursor: pointer;
                    color: inherit;
                    border-bottom: 2px solid transparent;
                ">调试</button>
            </div>
            
            <div class="panel-content" style="
                padding: 16px;
                height: calc(100% - 120px);
                overflow-y: auto;
            ">
                ${this.generateTabContent()}
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(this.panelElement);
        
        // 添加浮动按钮
        this.createFloatingButton();
    }
    
    /**
     * 创建浮动打开按钮
     * @UI 创建用于打开面板的浮动按钮
     */
    createFloatingButton() {
        const floatingBtn = document.createElement('div');
        floatingBtn.id = 'cache-monitor-float-btn';
        floatingBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #4299e1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 9998;
            font-size: 20px;
            color: white;
            transition: transform 0.2s;
        `;
        floatingBtn.innerHTML = '📊';
        floatingBtn.title = '打开缓存监控面板';
        
        floatingBtn.addEventListener('mouseenter', () => {
            floatingBtn.style.transform = 'scale(1.1)';
        });
        
        floatingBtn.addEventListener('mouseleave', () => {
            floatingBtn.style.transform = 'scale(1)';
        });
        
        floatingBtn.addEventListener('click', () => {
            this.showPanel();
        });
        
        document.body.appendChild(floatingBtn);
        this.floatingButton = floatingBtn;
    }
    
    /**
     * 生成标签页内容
     * @UI 生成各个标签页的HTML内容
     */
    generateTabContent() {
        return `
            <!-- 概览标签页 -->
            <div class="tab-content active" data-tab="overview">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                    <div class="metric-card" style="
                        padding: 12px;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                    ">
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">总体命中率</div>
                        <div id="overall-hit-rate" style="font-size: 24px; font-weight: 600; color: #10b981;">--.--%</div>
                    </div>
                    <div class="metric-card" style="
                        padding: 12px;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                    ">
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">平均响应时间</div>
                        <div id="avg-response-time" style="font-size: 24px; font-weight: 600; color: #3b82f6;">-- ms</div>
                    </div>
                    <div class="metric-card" style="
                        padding: 12px;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                    ">
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">缓存大小</div>
                        <div id="cache-size" style="font-size: 24px; font-weight: 600; color: #f59e0b;">-- MB</div>
                    </div>
                    <div class="metric-card" style="
                        padding: 12px;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                    ">
                        <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">节省时间</div>
                        <div id="time-saved" style="font-size: 24px; font-weight: 600; color: #8b5cf6;">-- ms</div>
                    </div>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px;">模块性能</h4>
                    <div id="module-performance" style="
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        overflow: hidden;
                    ">
                        <!-- 动态生成模块性能列表 -->
                    </div>
                </div>
                
                <div>
                    <h4 style="margin: 0 0 8px 0; font-size: 14px;">系统状态</h4>
                    <div id="system-health" style="
                        padding: 12px;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                    ">
                        <!-- 动态生成系统健康状态 -->
                    </div>
                </div>
            </div>
            
            <!-- 性能标签页 -->
            <div class="tab-content" data-tab="performance" style="display: none;">
                <div style="margin-bottom: 16px;">
                    <canvas id="hit-rate-chart" width="560" height="200" style="
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                    "></canvas>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                    <div>
                        <h4 style="margin: 0 0 8px 0; font-size: 14px;">响应时间趋势</h4>
                        <canvas id="response-time-chart" width="270" height="150"></canvas>
                    </div>
                    <div>
                        <h4 style="margin: 0 0 8px 0; font-size: 14px;">缓存大小变化</h4>
                        <canvas id="cache-size-chart" width="270" height="150"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 管理标签页 -->
            <div class="tab-content" data-tab="management" style="display: none;">
                <div style="margin-bottom: 16px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px;">缓存控制</h4>
                    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                        <button id="cache-enable-btn" class="control-btn" style="
                            padding: 6px 12px;
                            border: 1px solid #10b981;
                            border-radius: 4px;
                            background: #10b981;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                        ">启用缓存</button>
                        <button id="cache-disable-btn" class="control-btn" style="
                            padding: 6px 12px;
                            border: 1px solid #ef4444;
                            border-radius: 4px;
                            background: #ef4444;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                        ">禁用缓存</button>
                        <button id="debug-enable-btn" class="control-btn" style="
                            padding: 6px 12px;
                            border: 1px solid #f59e0b;
                            border-radius: 4px;
                            background: #f59e0b;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                        ">调试模式</button>
                    </div>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px;">模块管理</h4>
                    <div id="module-controls" style="
                        display: grid;
                        grid-template-columns: 1fr 1fr 1fr;
                        gap: 8px;
                    ">
                        <!-- 动态生成模块控制按钮 -->
                    </div>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px;">批量操作</h4>
                    <div style="display: flex; gap: 8px;">
                        <button id="clear-all-btn" style="
                            padding: 6px 12px;
                            border: 1px solid #ef4444;
                            border-radius: 4px;
                            background: none;
                            color: #ef4444;
                            cursor: pointer;
                            font-size: 12px;
                        ">清空所有缓存</button>
                        <button id="export-report-btn" style="
                            padding: 6px 12px;
                            border: 1px solid #3b82f6;
                            border-radius: 4px;
                            background: none;
                            color: #3b82f6;
                            cursor: pointer;
                            font-size: 12px;
                        ">导出报告</button>
                        <button id="warmup-cache-btn" style="
                            padding: 6px 12px;
                            border: 1px solid #8b5cf6;
                            border-radius: 4px;
                            background: none;
                            color: #8b5cf6;
                            cursor: pointer;
                            font-size: 12px;
                        ">缓存预热</button>
                    </div>
                </div>
                
                <div>
                    <h4 style="margin: 0 0 8px 0; font-size: 14px;">配置信息</h4>
                    <div id="cache-config" style="
                        padding: 12px;
                        border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                        border-radius: 6px;
                        background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                        font-family: monospace;
                        font-size: 11px;
                        white-space: pre-wrap;
                    ">
                        <!-- 动态生成配置信息 -->
                    </div>
                </div>
            </div>
            
            <!-- 调试标签页 -->
            <div class="tab-content" data-tab="debug" style="display: none;">
                <div style="margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="margin: 0; font-size: 14px;">调试日志</h4>
                        <button id="clear-logs-btn" style="
                            padding: 4px 8px;
                            border: 1px solid #6b7280;
                            border-radius: 4px;
                            background: none;
                            color: #6b7280;
                            cursor: pointer;
                            font-size: 11px;
                        ">清空日志</button>
                    </div>
                </div>
                
                <div id="debug-logs" style="
                    height: 280px;
                    padding: 12px;
                    border: 1px solid ${this.options.theme === 'dark' ? '#4a5568' : '#e2e8f0'};
                    border-radius: 6px;
                    background: ${this.options.theme === 'dark' ? '#1a202c' : '#f7fafc'};
                    font-family: monospace;
                    font-size: 11px;
                    overflow-y: auto;
                    white-space: pre-wrap;
                    line-height: 1.4;
                ">
                    <div style="color: #6b7280;">调试日志将显示在这里...</div>
                </div>
            </div>
        `;
    }
    
    /**
     * 绑定事件处理器
     * @EVENT 绑定各种用户交互事件
     */
    bindEventHandlers() {
        // 关闭按钮
        const closeBtn = this.panelElement.querySelector('#cache-close-btn');
        closeBtn.addEventListener('click', () => this.hidePanel());
        
        // 刷新按钮
        const refreshBtn = this.panelElement.querySelector('#cache-refresh-btn');
        refreshBtn.addEventListener('click', () => this.refreshData());
        
        // 标签页切换
        const tabButtons = this.panelElement.querySelectorAll('.tab-button');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });
        
        // 缓存控制按钮
        this.bindControlButtons();
    }
    
    /**
     * 绑定控制按钮事件
     * @EVENT 绑定缓存管理控制按钮
     */
    bindControlButtons() {
        // 启用/禁用缓存
        const enableBtn = this.panelElement.querySelector('#cache-enable-btn');
        const disableBtn = this.panelElement.querySelector('#cache-disable-btn');
        const debugBtn = this.panelElement.querySelector('#debug-enable-btn');
        
        if (enableBtn) {
            enableBtn.addEventListener('click', () => {
                if (this.integrationAdapter) {
                    this.integrationAdapter.enable();
                    this.addDebugLog('缓存功能已启用', 'success');
                }
            });
        }
        
        if (disableBtn) {
            disableBtn.addEventListener('click', () => {
                if (this.integrationAdapter) {
                    this.integrationAdapter.disable();
                    this.addDebugLog('缓存功能已禁用', 'warning');
                }
            });
        }
        
        if (debugBtn) {
            debugBtn.addEventListener('click', () => {
                if (this.integrationAdapter) {
                    this.integrationAdapter.enableDebugMode();
                    this.addDebugLog('调试模式已启用', 'info');
                }
            });
        }
        
        // 清空所有缓存
        const clearAllBtn = this.panelElement.querySelector('#clear-all-btn');
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', async () => {
                if (confirm('确定要清空所有缓存吗？')) {
                    await this.cacheManager.clear();
                    this.addDebugLog('所有缓存已清空', 'warning');
                    this.refreshData();
                }
            });
        }
        
        // 导出报告
        const exportBtn = this.panelElement.querySelector('#export-report-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportPerformanceReport());
        }
        
        // 清空日志
        const clearLogsBtn = this.panelElement.querySelector('#clear-logs-btn');
        if (clearLogsBtn) {
            clearLogsBtn.addEventListener('click', () => this.clearDebugLogs());
        }
    }
    
    /**
     * 显示面板
     * @UI 显示监控面板
     */
    showPanel() {
        this.panelElement.style.display = 'block';
        this.floatingButton.style.display = 'none';
        this.panelVisible = true;
        this.refreshData();
        
        // 添加显示动画
        this.panelElement.style.transform = 'translateY(20px)';
        this.panelElement.style.opacity = '0';
        
        setTimeout(() => {
            this.panelElement.style.transition = 'all 0.3s ease';
            this.panelElement.style.transform = 'translateY(0)';
            this.panelElement.style.opacity = '1';
        }, 10);
    }
    
    /**
     * 隐藏面板
     * @UI 隐藏监控面板
     */
    hidePanel() {
        this.panelElement.style.transform = 'translateY(20px)';
        this.panelElement.style.opacity = '0';
        
        setTimeout(() => {
            this.panelElement.style.display = 'none';
            this.floatingButton.style.display = 'flex';
            this.panelVisible = false;
        }, 300);
    }
    
    /**
     * 切换标签页
     * @UI 切换面板标签页
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        const tabButtons = this.panelElement.querySelectorAll('.tab-button');
        tabButtons.forEach(btn => {
            if (btn.getAttribute('data-tab') === tabName) {
                btn.classList.add('active');
                btn.style.borderBottomColor = '#3b82f6';
            } else {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
            }
        });
        
        // 更新内容显示
        const tabContents = this.panelElement.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            if (content.getAttribute('data-tab') === tabName) {
                content.classList.add('active');
                content.style.display = 'block';
            } else {
                content.classList.remove('active');
                content.style.display = 'none';
            }
        });
        
        // 特定标签页的额外处理
        if (tabName === 'performance' && this.options.enableCharts) {
            setTimeout(() => this.updateCharts(), 100);
        } else if (tabName === 'management') {
            this.updateManagementTab();
        } else if (tabName === 'debug') {
            this.updateDebugTab();
        }
    }
    
    /**
     * 刷新数据
     * @DATA 刷新所有监控数据
     */
    async refreshData() {
        try {
            // 获取缓存统计
            const cacheStats = this.cacheManager.getStats();
            
            // 获取性能报告
            const perfReport = this.integrationAdapter ? 
                this.integrationAdapter.getPerformanceReport() : null;
            
            // 获取健康状态
            const healthStatus = await this.cacheManager.getHealthStatus();
            
            // 更新概览数据
            this.updateOverviewTab(cacheStats, perfReport, healthStatus);
            
            // 更新图表数据
            if (this.options.enableCharts) {
                this.updateChartData(cacheStats, perfReport);
            }
            
            // 更新状态指示器
            this.updateStatusIndicator(healthStatus);
            
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.addDebugLog(`数据刷新失败: ${error.message}`, 'error');
        }
    }
    
    /**
     * 更新概览标签页
     * @UI 更新概览标签页的数据显示
     */
    updateOverviewTab(cacheStats, perfReport, healthStatus) {
        // 更新总体指标
        const overallHitRate = this.panelElement.querySelector('#overall-hit-rate');
        const avgResponseTime = this.panelElement.querySelector('#avg-response-time');
        const cacheSize = this.panelElement.querySelector('#cache-size');
        const timeSaved = this.panelElement.querySelector('#time-saved');
        
        if (overallHitRate) {
            overallHitRate.textContent = perfReport ? perfReport.overall.overallHitRate : cacheStats.hitRate;
        }
        
        if (avgResponseTime) {
            avgResponseTime.textContent = cacheStats.avgResponseTimeMs + ' ms';
        }
        
        if (cacheSize) {
            cacheSize.textContent = cacheStats.bytesStoredMB + ' MB';
        }
        
        if (timeSaved && perfReport) {
            timeSaved.textContent = perfReport.overall.totalTimeSaved;
        }
        
        // 更新模块性能
        this.updateModulePerformanceList(perfReport);
        
        // 更新系统健康状态
        this.updateSystemHealthDisplay(healthStatus);
    }
    
    /**
     * 更新模块性能列表
     * @UI 更新模块性能显示列表
     */
    updateModulePerformanceList(perfReport) {
        const container = this.panelElement.querySelector('#module-performance');
        if (!container || !perfReport) return;
        
        let html = '';
        for (const [module, stats] of Object.entries(perfReport.byModule)) {
            const hitRateNum = parseFloat(stats.hitRate);
            const hitRateColor = hitRateNum > 70 ? '#10b981' : hitRateNum > 40 ? '#f59e0b' : '#ef4444';
            
            html += `
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    border-bottom: 1px solid ${this.options.theme === 'dark' ? '#374151' : '#f3f4f6'};
                ">
                    <div>
                        <div style="font-size: 13px; font-weight: 500;">${module}</div>
                        <div style="font-size: 11px; color: #6b7280;">${stats.calls} 次调用</div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 14px; font-weight: 600; color: ${hitRateColor};">${stats.hitRate}</div>
                        <div style="font-size: 11px; color: #6b7280;">${stats.avgResponseTime}</div>
                    </div>
                </div>
            `;
        }
        
        container.innerHTML = html || '<div style="padding: 12px; text-align: center; color: #6b7280;">暂无数据</div>';
    }
    
    /**
     * 更新系统健康状态显示
     * @UI 更新系统健康状态
     */
    updateSystemHealthDisplay(healthStatus) {
        const container = this.panelElement.querySelector('#system-health');
        if (!container || !healthStatus) return;
        
        const statusColor = healthStatus.status === 'healthy' ? '#10b981' : 
                           healthStatus.status === 'warning' ? '#f59e0b' : '#ef4444';
        
        const statusText = healthStatus.status === 'healthy' ? '健康' :
                          healthStatus.status === 'warning' ? '警告' : '错误';
        
        let html = `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                <div style="
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: ${statusColor};
                "></div>
                <span style="font-weight: 500;">系统状态: ${statusText}</span>
            </div>
        `;
        
        if (healthStatus.backends) {
            html += '<div style="font-size: 12px; color: #6b7280; margin-bottom: 8px;">存储后端:</div>';
            
            for (const [backend, status] of Object.entries(healthStatus.backends)) {
                const backendColor = status.status === 'healthy' ? '#10b981' : '#ef4444';
                html += `
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 4px 0;
                        font-size: 11px;
                    ">
                        <span>${backend}</span>
                        <span style="color: ${backendColor};">${status.status}</span>
                    </div>
                `;
            }
        }
        
        if (healthStatus.issues && healthStatus.issues.length > 0) {
            html += '<div style="margin-top: 8px; font-size: 11px; color: #ef4444;">';
            html += '<div>问题列表:</div>';
            for (const issue of healthStatus.issues) {
                html += `<div>• ${issue}</div>`;
            }
            html += '</div>';
        }
        
        container.innerHTML = html;
    }
    
    /**
     * 更新状态指示器
     * @UI 更新面板标题栏的状态指示器
     */
    updateStatusIndicator(healthStatus) {
        const indicator = this.panelElement.querySelector('#cache-status-indicator');
        if (!indicator) return;
        
        const color = healthStatus.status === 'healthy' ? '#10b981' : 
                     healthStatus.status === 'warning' ? '#f59e0b' : '#ef4444';
        
        indicator.style.background = color;
    }
    
    /**
     * 启动自动刷新
     * @TIMER 启动定时刷新数据
     */
    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            if (this.panelVisible) {
                this.refreshData();
            }
        }, this.options.refreshInterval);
    }
    
    /**
     * 启动图表更新
     * @CHART 启动图表数据更新
     */
    startChartUpdates() {
        if (this.chartTimer) {
            clearInterval(this.chartTimer);
        }
        
        this.chartTimer = setInterval(() => {
            if (this.panelVisible && this.options.enableCharts) {
                // 只有在性能标签页激活时才更新图表
                const perfTab = this.panelElement.querySelector('.tab-content[data-tab="performance"]');
                if (perfTab && perfTab.style.display !== 'none') {
                    this.updateCharts();
                }
            }
        }, this.options.chartUpdateInterval);
    }
    
    /**
     * 更新图表数据
     * @CHART 更新图表显示数据
     */
    updateChartData(cacheStats, perfReport) {
        const now = Date.now();
        
        // 添加新数据点
        this.chartData.timestamps.push(now);
        this.chartData.hitRates.push(parseFloat(cacheStats.hitRate) || 0);
        this.chartData.responseTimes.push(parseFloat(cacheStats.avgResponseTimeMs) || 0);
        this.chartData.cacheSize.push(parseFloat(cacheStats.bytesStoredMB) || 0);
        
        // 保持数据点数量限制
        if (this.chartData.timestamps.length > this.options.maxDataPoints) {
            this.chartData.timestamps.shift();
            this.chartData.hitRates.shift();
            this.chartData.responseTimes.shift();
            this.chartData.cacheSize.shift();
        }
    }
    
    /**
     * 更新图表显示
     * @CHART 绘制或更新图表
     */
    updateCharts() {
        this.drawHitRateChart();
        this.drawResponseTimeChart();
        this.drawCacheSizeChart();
    }
    
    /**
     * 绘制命中率图表
     * @CHART 绘制缓存命中率趋势图
     */
    drawHitRateChart() {
        const canvas = this.panelElement.querySelector('#hit-rate-chart');
        if (!canvas || !this.chartData.hitRates.length) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 设置样式
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 2;
        ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
        
        // 绘制网格线
        ctx.strokeStyle = this.options.theme === 'dark' ? '#374151' : '#e5e7eb';
        ctx.lineWidth = 1;
        
        for (let i = 0; i <= 5; i++) {
            const y = (height / 5) * i;
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
        
        // 绘制数据线
        if (this.chartData.hitRates.length > 1) {
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const stepX = width / (this.chartData.hitRates.length - 1);
            
            for (let i = 0; i < this.chartData.hitRates.length; i++) {
                const x = i * stepX;
                const y = height - (this.chartData.hitRates[i] / 100) * height;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
        }
    }
    
    /**
     * 绘制响应时间图表
     * @CHART 绘制响应时间趋势图
     */
    drawResponseTimeChart() {
        const canvas = this.panelElement.querySelector('#response-time-chart');
        if (!canvas || !this.chartData.responseTimes.length) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 简化的图表绘制
        if (this.chartData.responseTimes.length > 1) {
            ctx.strokeStyle = '#f59e0b';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const maxValue = Math.max(...this.chartData.responseTimes);
            const stepX = width / (this.chartData.responseTimes.length - 1);
            
            for (let i = 0; i < this.chartData.responseTimes.length; i++) {
                const x = i * stepX;
                const y = height - (this.chartData.responseTimes[i] / maxValue) * height;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
        }
    }
    
    /**
     * 绘制缓存大小图表
     * @CHART 绘制缓存大小趋势图
     */
    drawCacheSizeChart() {
        const canvas = this.panelElement.querySelector('#cache-size-chart');
        if (!canvas || !this.chartData.cacheSize.length) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 简化的图表绘制
        if (this.chartData.cacheSize.length > 1) {
            ctx.strokeStyle = '#8b5cf6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const maxValue = Math.max(...this.chartData.cacheSize) || 1;
            const stepX = width / (this.chartData.cacheSize.length - 1);
            
            for (let i = 0; i < this.chartData.cacheSize.length; i++) {
                const x = i * stepX;
                const y = height - (this.chartData.cacheSize[i] / maxValue) * height;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
        }
    }
    
    /**
     * 更新管理标签页
     * @UI 更新管理标签页内容
     */
    updateManagementTab() {
        // 更新配置信息
        const configContainer = this.panelElement.querySelector('#cache-config');
        if (configContainer) {
            const config = {
                cacheManager: this.cacheManager.options,
                integrationAdapter: this.integrationAdapter ? this.integrationAdapter.options : null
            };
            configContainer.textContent = JSON.stringify(config, null, 2);
        }
        
        // 更新模块控制按钮
        this.updateModuleControls();
    }
    
    /**
     * 更新模块控制区域
     * @UI 更新模块控制按钮区域
     */
    updateModuleControls() {
        const container = this.panelElement.querySelector('#module-controls');
        if (!container) return;
        
        const modules = ['gemini', 'address', 'channel'];
        let html = '';
        
        for (const module of modules) {
            html += `
                <button class="module-clear-btn" data-module="${module}" style="
                    padding: 6px 12px;
                    border: 1px solid #6b7280;
                    border-radius: 4px;
                    background: none;
                    color: inherit;
                    cursor: pointer;
                    font-size: 11px;
                ">清理${module}</button>
            `;
        }
        
        container.innerHTML = html;
        
        // 绑定清理按钮事件
        container.querySelectorAll('.module-clear-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const module = e.target.getAttribute('data-module');
                if (this.integrationAdapter) {
                    await this.integrationAdapter.clearModuleCache(module);
                    this.addDebugLog(`${module} 模块缓存已清理`, 'info');
                    this.refreshData();
                }
            });
        });
    }
    
    /**
     * 更新调试标签页
     * @UI 更新调试标签页内容
     */
    updateDebugTab() {
        this.updateDebugLogs();
    }
    
    /**
     * 设置调试日志监听
     * @DEBUG 设置调试日志收集
     */
    setupDebugLogging() {
        // 拦截console.log来收集日志（可选）
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        console.log = (...args) => {
            originalLog.apply(console, args);
            const message = args.join(' ');
            if (message.includes('缓存') || message.includes('Cache')) {
                this.addDebugLog(message, 'info');
            }
        };
        
        console.warn = (...args) => {
            originalWarn.apply(console, args);
            const message = args.join(' ');
            if (message.includes('缓存') || message.includes('Cache')) {
                this.addDebugLog(message, 'warning');
            }
        };
        
        console.error = (...args) => {
            originalError.apply(console, args);
            const message = args.join(' ');
            if (message.includes('缓存') || message.includes('Cache')) {
                this.addDebugLog(message, 'error');
            }
        };
    }
    
    /**
     * 添加调试日志
     * @DEBUG 添加新的调试日志条目
     */
    addDebugLog(message, level = 'info') {
        const timestamp = new Date().toISOString().substr(11, 8);
        const logEntry = {
            timestamp: timestamp,
            level: level,
            message: message
        };
        
        this.debugLogs.push(logEntry);
        
        // 保持日志数量限制
        if (this.debugLogs.length > this.maxLogs) {
            this.debugLogs.shift();
        }
        
        // 如果调试标签页当前可见，更新显示
        if (this.panelVisible) {
            const debugTab = this.panelElement.querySelector('.tab-content[data-tab="debug"]');
            if (debugTab && debugTab.style.display !== 'none') {
                this.updateDebugLogs();
            }
        }
    }
    
    /**
     * 更新调试日志显示
     * @DEBUG 更新调试日志显示区域
     */
    updateDebugLogs() {
        const container = this.panelElement.querySelector('#debug-logs');
        if (!container) return;
        
        if (this.debugLogs.length === 0) {
            container.innerHTML = '<div style="color: #6b7280;">暂无调试日志...</div>';
            return;
        }
        
        let html = '';
        for (const log of this.debugLogs) {
            const levelColor = {
                'info': '#3b82f6',
                'success': '#10b981',
                'warning': '#f59e0b',
                'error': '#ef4444'
            }[log.level] || '#6b7280';
            
            html += `
                <div style="margin-bottom: 4px;">
                    <span style="color: #6b7280;">[${log.timestamp}]</span>
                    <span style="color: ${levelColor}; font-weight: 500;">[${log.level.toUpperCase()}]</span>
                    <span>${log.message}</span>
                </div>
            `;
        }
        
        container.innerHTML = html;
        container.scrollTop = container.scrollHeight;
    }
    
    /**
     * 清空调试日志
     * @DEBUG 清空所有调试日志
     */
    clearDebugLogs() {
        this.debugLogs = [];
        this.updateDebugLogs();
    }
    
    /**
     * 导出性能报告
     * @EXPORT 导出详细的性能分析报告
     */
    async exportPerformanceReport() {
        try {
            const report = {
                exportTime: new Date().toISOString(),
                cacheStats: this.cacheManager.getStats(),
                performanceReport: this.integrationAdapter ? 
                    this.integrationAdapter.getPerformanceReport() : null,
                healthStatus: await this.cacheManager.getHealthStatus(),
                chartData: this.chartData,
                debugLogs: this.debugLogs
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { 
                type: 'application/json' 
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cache-performance-report-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            this.addDebugLog('性能报告已导出', 'success');
            
        } catch (error) {
            console.error('导出报告失败:', error);
            this.addDebugLog(`导出报告失败: ${error.message}`, 'error');
        }
    }
    
    /**
     * 销毁监控面板
     * @CLEANUP 清理所有资源
     */
    dispose() {
        // 清理定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        if (this.chartTimer) {
            clearInterval(this.chartTimer);
        }
        
        // 移除DOM元素
        if (this.panelElement) {
            this.panelElement.remove();
        }
        
        if (this.floatingButton) {
            this.floatingButton.remove();
        }
        
        console.log('🗑️ 缓存监控面板已销毁');
    }
}

// 模块工厂函数
function createCacheMonitorPanelModule(container) {
    let cacheManager = null;
    let integrationAdapter = null;
    
    try {
        if (container) {
            cacheManager = container.has('cacheManager') ? container.get('cacheManager') : null;
            integrationAdapter = container.has('cacheIntegrationAdapter') ? 
                container.get('cacheIntegrationAdapter') : null;
        }
    } catch (error) {
        console.warn('⚠️ 无法获取缓存监控依赖，将使用向后兼容模式');
    }
    
    // 向后兼容
    if (!cacheManager && typeof window !== 'undefined' && window.cacheManager) {
        cacheManager = window.cacheManager;
    }
    
    if (!integrationAdapter && typeof window !== 'undefined' && window.cacheIntegrationAdapter) {
        integrationAdapter = window.cacheIntegrationAdapter;
    }
    
    if (!cacheManager) {
        console.error('❌ 缓存管理器不可用，缓存监控面板将无法工作');
        return null;
    }
    
    return new CacheMonitorPanel(cacheManager, integrationAdapter);
}

// 向后兼容支持
if (typeof window !== 'undefined' && !window.moduleContainer) {
    console.warn('⚠️ 检测到传统模式，创建缓存监控面板兼容实例');
    if (window.cacheManager) {
        window.cacheMonitorPanel = new CacheMonitorPanel(
            window.cacheManager, 
            window.cacheIntegrationAdapter
        );
    }
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('cacheMonitorPanel', createCacheMonitorPanelModule, 
        ['cacheManager', 'cacheIntegrationAdapter']);
    console.log('📦 CacheMonitorPanel已注册到模块容器');
}