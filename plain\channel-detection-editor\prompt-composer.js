/**
 * 提示词组合器模块 - 提示词片段组合引擎
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：prompt-editor.js（片段源）, prompt-segmenter.js（片段处理）
 * 被依赖：app.js（动态提示词生成）, gemini-config.js（提示词构建）
 * 全局变量：创建 PromptComposer 实例（非全局）
 * 组合引擎：片段获取、智能合并、缓存优化、API集成
 * 
 * === 核心功能 ===
 * - 动态提示词片段组合和生成
 * - 多源片段获取（本地/远程）
 * - 智能片段合并和冲突解决
 * - 缓存优化和性能提升
 * - API集成支持远程模板
 * 
 * === 集成点 ===
 * - app.js：根据用户选择生成动态提示词
 * - prompt-editor.js：获取存储的提示词片段
 * - prompt-segmenter.js：处理和分析片段内容
 * - gemini-config.js：构建完整的API提示词
 * 
 * === 使用场景 ===
 * - 根据渠道和字段需求动态生成提示词
 * - 多片段智能组合和优化
 * - 远程模板的获取和缓存
 * - 提示词生成性能优化
 * 
 * === 注意事项 ===
 * 提示词片段组合引擎
 * 支持本地片段和远程API模板
 * 包含缓存机制提升性能
 * 智能合并算法避免内容冲突
 */

// 提示词片段组合引擎

class PromptComposer {
  constructor() {
    this.apiBaseUrl = 'http://localhost:3001';
    this.templateCache = new Map();
  }

  /**
   * 组合完整的提示词 - 支持字段模块化
   * @param {string} channelId - 渠道ID
   * @param {Array} requestedFields - 请求的字段列表
   * @param {string} inputText - 输入文本（用于字段模块化组合）
   */
  async composePrompt(channelId, requestedFields = null, inputText = '') {
    try {
      // 获取字段片段（渠道独立存储 + generic回退）
      const fieldSnippets = await this.getFieldSnippets(channelId, requestedFields);

      let composedPrompt = '';

      // 优先使用字段模块化组合（如果有输入文本）
      if (inputText && Object.keys(fieldSnippets).length > 0) {
        // 使用内置的字段模块化组合（不再依赖 PromptFragmentManager）
        composedPrompt = this.buildModularPromptInternal(inputText, channelId, fieldSnippets);
        console.log('🔧 使用字段模块化组合提示词');
      } else {
        // 传统组合方式（向后兼容）
        const baseTemplate = await this.getSnippet('base', null);
        composedPrompt = this.mergeSnippets(baseTemplate, fieldSnippets);
        console.log('🔄 使用传统组合方式');
      }

      return {
        success: true,
        composedPrompt: composedPrompt,
        segments: fieldSnippets,
        channelId: channelId,
        isModular: !!inputText && Object.keys(fieldSnippets).length > 0
      };

    } catch (error) {
      console.error('组合提示词失败:', error);
      return {
        success: false,
        error: error.message,
        composedPrompt: ''
      };
    }
  }

  /**
   * 统一服务获取工具
   * @deprecated 使用全局 window.getService() 替代
   * @param {string} serviceName - 服务名称
   * @returns {Object|null} 服务实例
   */
  getService(serviceName) {
    return window.getService ? window.getService(serviceName) : null;
  }

  /**
   * 获取 PromptFragmentManager 实例
   * @deprecated 使用 getService('promptFragmentManager') 替代
   */
  getFragmentManager() {
    return this.getService('promptFragmentManager');
  }

  /**
   * 获取字段片段 - 渠道独立存储 + generic回退机制
   *
   * 实现策略：
   * 1. 优先获取指定渠道的字段片段 (channelId)
   * 2. 如果渠道特定片段不存在，自动回退到通用渠道 (generic)
   * 3. 确保每个渠道的字段片段独立存储，避免跨渠道污染
   */
  async getFieldSnippets(channelId, requestedFields) {
    let fields = requestedFields;

    // 如果没有指定字段，获取启用的字段
    if (!fields && window.promptEditor) {
      const enabledFields = window.promptEditor.getEnabledFields(channelId);
      fields = Object.keys(enabledFields);
    }

    // 回退到默认检测
    if (!fields || fields.length === 0) {
      fields = await this.detectRequiredFields(channelId);
    }

    const snippets = {};

    for (const field of fields) {
      // 优先获取渠道特定片段 (渠道独立存储)
      let snippet = await this.getSnippet(field, channelId);

      // 回退机制：如果没有渠道特定片段，使用通用渠道 (generic) 的片段
      if (!snippet && channelId && channelId !== 'generic') {
        snippet = await this.getSnippet(field, 'generic');
        if (snippet) {
          console.log(`📋 字段 ${field} 回退到通用模板 (${channelId} -> generic)`);
        }
      }

      if (snippet) {
        snippets[field] = snippet;
      }
    }

    return snippets;
  }

  /**
   * 获取单个片段 - 支持新的数据结构
   */
  async getSnippet(field, channelId) {
    const cacheKey = `${field}_${channelId || 'generic'}`;

    // 检查缓存
    if (this.templateCache.has(cacheKey)) {
      return this.templateCache.get(cacheKey);
    }

    try {
      // 优先从prompt-editor获取（支持新数据结构）
      if (window.promptEditor && window.promptEditor.promptSnippets) {
        const channelData = window.promptEditor.promptSnippets[channelId || 'generic'];
        if (channelData && channelData[field]) {
          const fieldData = window.promptEditor.migrateDataFormat(channelData[field]);
          if (fieldData.enabled && fieldData.content) {
            this.templateCache.set(cacheKey, fieldData.content);
            return fieldData.content;
          }
        }
      }

      // 从API获取
      const response = await fetch(
        `${this.apiBaseUrl}/api/prompts?field=${field}&channelId=${channelId || ''}`
      );

      if (response.ok) {
        const snippets = await response.json();
        if (snippets.length > 0) {
          const content = snippets[0].content;
          this.templateCache.set(cacheKey, content);
          return content;
        }
      }

      // 回退到localStorage
      const localStorageKey = `prompt_snippet_${channelId || 'generic'}_${field}`;
      const localContent = localStorage.getItem(localStorageKey);

      if (localContent) {
        this.templateCache.set(cacheKey, localContent);
        return localContent;
      }

      return null;

    } catch (error) {
      console.warn(`获取片段失败: ${cacheKey}`, error);
      return null;
    }
  }

  /**
   * 内置字段模块化组合 - 替代 PromptFragmentManager.buildModularPrompt
   * @param {string} inputText - 输入文本
   * @param {string} channelId - 渠道ID
   * @param {Object} fieldSnippets - 字段片段映射
   * @returns {string} 组合后的提示词
   */
  buildModularPromptInternal(inputText, channelId, fieldSnippets) {
    const parts = [];

    // 1. 基础角色和格式
    parts.push('You are an expert booking data extraction system. Extract ALL information with maximum accuracy.');
    parts.push('Output EXACTLY one JSON object. No explanations, no additional text.');
    parts.push('');

    // 2. 字段模块化组合 - 使用字段片段
    parts.push('REQUIRED FIELDS (set null if not found):');
    for (const [field, snippet] of Object.entries(fieldSnippets)) {
      parts.push(`- ${field}: ${snippet}`);
    }
    parts.push('');

    // 3. 输入文本
    parts.push('INPUT TEXT TO ANALYZE:');
    parts.push('---');
    parts.push(inputText);
    parts.push('---');

    return parts.join('\n');
  }

  /**
   * 合并片段到模板
   */
  mergeSnippets(baseTemplate, fieldSnippets) {
    let result = baseTemplate || '';
    
    // 简单的占位符替换
    for (const [field, snippet] of Object.entries(fieldSnippets)) {
      const placeholder = `{${field}}`;
      if (result.includes(placeholder)) {
        result = result.replace(placeholder, snippet);
      } else {
        // 如果没有占位符，追加到末尾
        result += '\n\n' + snippet;
      }
    }
    
    // 清理多余的空白行
    result = result.replace(/\n{3,}/g, '\n\n').trim();
    
    return result;
  }



  /**
   * 检测所需字段
   */
  async detectRequiredFields(channelId) {
    // 根据渠道类型推断需要的字段
    const channelType = await this.getChannelType(channelId);
    
    const fieldSets = {
      ota: ['ota', 'ota_price', 'customer', 'flight', 'location'],
      ride: ['customer', 'pickup', 'destination', 'requirements'],
      delivery: ['customer', 'pickup', 'destination', 'requirements'],
      default: ['validation', 'schema', 'customer', 'requirements']
    };
    
    return fieldSets[channelType] || fieldSets.default;
  }

  /**
   * 获取渠道类型
   */
  async getChannelType(channelId) {
    if (!channelId) return 'default';
    
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/channels/${channelId}`);
      if (response.ok) {
        const channel = await response.json();
        // 从渠道名称推断类型
        if (channel.name.includes('flight') || channel.name.includes('ota')) {
          return 'ota';
        } else if (channel.name.includes('ride') || channel.name.includes('taxi')) {
          return 'ride';
        } else if (channel.name.includes('delivery') || channel.name.includes('快递')) {
          return 'delivery';
        }
      }
    } catch (error) {
      console.warn('获取渠道类型失败:', error);
    }
    
    return 'default';
  }

  /**
   * 验证组合结果
   */
  validateComposedPrompt(prompt) {
    const issues = [];
    
    // 检查长度
    if (prompt.length > 4000) {
      issues.push('提示词过长，可能会影响模型性能');
    }
    
    // 检查未替换的占位符
    const unmatchedPlaceholders = prompt.match(/\{[^}]+\}/g);
    if (unmatchedPlaceholders) {
      issues.push(`存在未替换的占位符: ${unmatchedPlaceholders.join(', ')}`);
    }
    
    // 检查JSON格式（如果包含）
    if (prompt.includes('{') && prompt.includes('}')) {
      const jsonMatch = prompt.match(/\{[\s\S]*?\}/);
      if (jsonMatch) {
        try {
          JSON.parse(jsonMatch[0]);
        } catch (e) {
          issues.push('包含无效的JSON格式');
        }
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues: issues,
      length: prompt.length,
      lineCount: prompt.split('\n').length
    };
  }

  /**
   * 智能片段选择
   */
  async smartSegmentSelection(basePrompt, availableSnippets) {
    const selectedSnippets = {};
    
    // 分析基础模板中的占位符
    const placeholders = this.extractPlaceholders(basePrompt);
    
    // 优先选择有占位符的字段
    for (const field of placeholders) {
      if (availableSnippets[field]) {
        selectedSnippets[field] = availableSnippets[field];
      }
    }
    
    // 添加其他重要字段
    const importantFields = ['validation', 'schema', 'requirements'];
    for (const field of importantFields) {
      if (availableSnippets[field] && !selectedSnippets[field]) {
        selectedSnippets[field] = availableSnippets[field];
      }
    }
    
    return selectedSnippets;
  }

  /**
   * 提取占位符
   */
  extractPlaceholders(text) {
    const matches = text.match(/\{([^}]+)\}/g) || [];
    return matches.map(m => m.replace(/[{}]/g, ''));
  }

  /**
   * 缓存管理
   */
  clearCache() {
    this.templateCache.clear();
  }

  cacheSnippet(field, channelId, content) {
    const cacheKey = `${field}_${channelId || 'generic'}`;
    this.templateCache.set(cacheKey, content);
  }

  /**
   * 批量组合提示词
   */
  async batchCompose(requests) {
    const results = [];
    
    for (const request of requests) {
      try {
        const result = await this.composePrompt(request.channelId, request.fields);
        results.push({
          request: request,
          result: result,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        results.push({
          request: request,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    return results;
  }

  /**
   * 创建组合预览
   */
  async createPreview(channelId, fields = null) {
    const result = await this.composePrompt(channelId, fields);
    
    if (result.success) {
      const validation = this.validateComposedPrompt(result.composedPrompt);
      
      return {
        preview: result.composedPrompt,
        validation: validation,
        segments: result.segments,
        segmentCount: Object.keys(result.segments).length,
        totalLength: result.composedPrompt.length
      };
    }
    
    return {
      error: result.error,
      preview: ''
    };
  }

  /**
   * 导出组合配置
   */
  exportCompositionConfig(channelId, fields) {
    return {
      channelId: channelId,
      fields: fields,
      timestamp: new Date().toISOString(),
      version: '1.0',
      config: {
        baseTemplate: true,
        fieldSelection: 'auto',
        validation: true,
        cacheEnabled: true
      }
    };
  }
}

// 创建全局实例
window.promptComposer = new PromptComposer();

// 工具函数
async function composePrompt() {
  const channelId = document.getElementById('channel-select').value;
  const fields = document.getElementById('fields-select').value?.split(',');
  
  const result = await window.promptComposer.composePrompt(channelId, fields);
  
  if (result.success) {
    document.getElementById('output').value = result.composedPrompt;
    console.log('组合成功:', result);
  } else {
    alert('组合失败: ' + result.error);
  }
}

async function previewPrompt() {
  const channelId = document.getElementById('channel-select').value;
  const preview = await window.promptComposer.createPreview(channelId);
  
  if (preview.preview) {
    document.getElementById('preview').textContent = preview.preview;
    console.log('预览验证:', preview.validation);
  } else {
    alert('预览失败: ' + preview.error);
  }
}

function clearCache() {
  window.promptComposer.clearCache();
  alert('缓存已清除');
}