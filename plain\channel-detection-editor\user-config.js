/**
 * 用户配置模块 - 从config.js拆分
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（独立的用户配置模块）
 * 被依赖：目前未被其他模块使用（备用模块）
 * 全局变量：创建 window.userConfig 实例
 * 用户管理：用户信息、角色分组、用户CRUD操作
 * 
 * === 核心功能 ===
 * - 用户信息管理（增删改查）
 * - 多条件用户查找（ID/邮箱/名称）
 * - 角色型用户分组管理
 * - 用户数据导入导出
 * - 自动ID生成机制
 * 
 * === 集成点 ===
 * - 备用模块，可以替代 config.js 中的用户管理功能
 * - 目前与主系统没有直接集成
 * - 可与 config.js 的权限系统互补
 * 
 * === 使用场景 ===
 * - 用户信息的集中化管理
 * - 用户数据的批量操作
 * - 角色型用户管理系统
 * - 用户数据导入导出需求
 * 
 * === 注意事项 ===
 * 该模块与 config.js 中的用户数据有重复，需谨慎使用
 * 包含真实用户数据（邮箱、电话），注意数据安全
 * 用户ID自增机制可能与主系统冲突
 * 如果使用该模块，需要与 config.js 保持数据同步
 */

// 用户配置模块 - 从config.js拆分

class UserConfig {
    constructor() {
        this.users = this.loadUsers();
    }

    /**
     * 加载用户数据
     */
    loadUsers() {
        return [
            // 系统管理员
            { id: 1, name: 'Super Admin', email: '', phone: '', role_id: 1 },

            // 核心用户
            { id: 37, name: 'smw', email: '<EMAIL>', phone: '0162234711', role_id: 2 },
            { id: 420, name: 'chongyoonlim', email: '<EMAIL>', phone: '0167112699', role_id: 2 },
            { id: 533, name: 'xhs', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 1201, name: 'KK Lucas', email: '<EMAIL>', phone: '+601153392333', role_id: 2 },
            { id: 2446, name: 'UCSI - Cheras', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 2666, name: 'JRCoach', email: '<EMAIL>', phone: '', role_id: 2 },

            // 新增活跃用户
            { id: 2793, name: 'eramaztravel', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 2788, name: 'kai -JB', email: '<EMAIL>', phone: '+60167878373', role_id: 2 },
            { id: 2765, name: 'Mytravelexpert', email: '<EMAIL>', phone: '+60167788740', role_id: 2 },
            { id: 2732, name: 'oceanblue', email: '<EMAIL>', phone: '+60127697117', role_id: 2 },
            { id: 2847, name: 'KelvinLim', email: '<EMAIL>', phone: '', role_id: 2 },

            // 现有用户
            { id: 89, name: 'GMH Sabah', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 310, name: 'Jcy', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 312, name: 'opVenus', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 313, name: 'opEric', email: '', phone: '', role_id: 2 },
            { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
            { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
            { id: 421, name: 'josua', email: '<EMAIL>', phone: '', role_id: 2 },
            { id: 428, name: 'Gomyhire Yong', email: '', phone: '', role_id: 2 },
            // removed placeholder/test user
            { id: 622, name: 'CsBob', email: '', phone: '', role_id: 2 },
            { id: 777, name: '空空', email: '空空@gomyhire.com', phone: '', role_id: 2 },
            { id: 812, name: '淼淼', email: '', phone: '', role_id: 2 },
            { id: 856, name: 'GMH Ashley', email: '', phone: '', role_id: 2 }
        ];
    }

    /**
     * 根据ID查找用户
     */
    findUserById(id) {
        return this.users.find(user => user.id === id);
    }

    /**
     * 根据邮箱查找用户
     */
    findUserByEmail(email) {
        const normalizedEmail = email.toLowerCase();
        return this.users.find(user => 
            user.email && user.email.toLowerCase() === normalizedEmail
        );
    }

    /**
     * 根据名称查找用户
     */
    findUserByName(name) {
        return this.users.find(user => 
            user.name && user.name.toLowerCase().includes(name.toLowerCase())
        );
    }

    /**
     * 获取所有用户
     */
    getAllUsers() {
        return this.users;
    }

    /**
     * 获取角色用户
     */
    getUsersByRole(roleId) {
        return this.users.filter(user => user.role_id === roleId);
    }

    /**
     * 添加新用户
     */
    addUser(user) {
        // 生成新ID
        const newId = Math.max(...this.users.map(u => u.id), 0) + 1;
        const newUser = { ...user, id: newId };
        this.users.push(newUser);
        return newUser;
    }

    /**
     * 更新用户
     */
    updateUser(id, updates) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
            this.users[userIndex] = { ...this.users[userIndex], ...updates };
            return this.users[userIndex];
        }
        return null;
    }

    /**
     * 删除用户
     */
    deleteUser(id) {
        const userIndex = this.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
            return this.users.splice(userIndex, 1)[0];
        }
        return null;
    }

    /**
     * 导出用户数据
     */
    exportUsers() {
        return JSON.stringify(this.users, null, 2);
    }

    /**
     * 导入用户数据
     */
    importUsers(jsonData) {
        try {
            const users = JSON.parse(jsonData);
            if (Array.isArray(users)) {
                this.users = users;
                return true;
            }
        } catch (error) {
            console.error('导入用户数据失败:', error);
        }
        return false;
    }
}

// 创建全局实例
window.userConfig = new UserConfig();