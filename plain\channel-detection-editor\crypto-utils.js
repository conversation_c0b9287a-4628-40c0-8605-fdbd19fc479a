/**
 * 加密工具模块 - 用于localStorage数据加密
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（使用浏览器原生Web Crypto API）
 * 被依赖：local-storage-manager.js（可选安全存储）
 * 全局变量：创建 window.cryptoUtils 实例
 * 加密算法：AES-GCM 256位加密，PBKDF2密钥派生
 * 
 * === 核心功能 ===
 * - 提供AES-GCM加密/解密服务
 * - 支持数据完整性校验（SHA-256哈希）
 * - 安全的localStorage存储（带时间戳和哈希验证）
 * - 降级处理机制（加密失败时使用明文存储）
 * 
 * === 集成点 ===
 * - local-storage-manager.js：可选择使用加密存储功能
 * - 所有需要安全存储的模块都可通过window.cryptoUtils访问
 * 
 * === 使用场景 ===
 * - 敏感配置数据存储（API密钥、用户偏好）
 * - 临时数据的安全缓存
 * - 数据完整性验证需求
 * 
 * === 注意事项 ===
 * 该模块为可选功能，加密失败时会降级到明文存储
 * 使用固定盐值，实际生产环境应使用更安全的密钥管理
 * 支持浏览器Web Crypto API的现代浏览器
 */

// 简单的加密工具 - 用于localStorage数据加密

class CryptoUtils {
    constructor() {
        // 使用固定的盐值（在实际项目中应该更复杂）
        this.salt = 'channel_detection_editor_2024';
        this.encoder = new TextEncoder();
        this.decoder = new TextDecoder();
    }

    /**
     * 生成简单的哈希键
     */
    async generateKey(password = '') {
        const keyMaterial = await window.crypto.subtle.importKey(
            'raw',
            this.encoder.encode(password + this.salt),
            'PBKDF2',
            false,
            ['deriveKey']
        );

        return await window.crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: this.encoder.encode(this.salt),
                iterations: 100000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            false,
            ['encrypt', 'decrypt']
        );
    }

    /**
     * 加密数据
     */
    async encryptData(data, password = 'default_password') {
        try {
            const key = await this.generateKey(password);
            const iv = window.crypto.getRandomValues(new Uint8Array(12));
            
            const encrypted = await window.crypto.subtle.encrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                key,
                this.encoder.encode(JSON.stringify(data))
            );

            // 组合IV和加密数据
            const result = new Uint8Array(iv.length + encrypted.byteLength);
            result.set(iv, 0);
            result.set(new Uint8Array(encrypted), iv.length);

            return btoa(String.fromCharCode(...result));
            
        } catch (error) {
            console.error('加密失败:', error);
            // 加密失败时返回原始数据
            return JSON.stringify(data);
        }
    }

    /**
     * 解密数据
     */
    async decryptData(encryptedData, password = 'default_password') {
        try {
            // 解码Base64
            const binaryData = atob(encryptedData);
            const data = new Uint8Array(binaryData.length);
            for (let i = 0; i < binaryData.length; i++) {
                data[i] = binaryData.charCodeAt(i);
            }

            // 提取IV和加密数据
            const iv = data.slice(0, 12);
            const encrypted = data.slice(12);

            const key = await this.generateKey(password);
            
            const decrypted = await window.crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: iv
                },
                key,
                encrypted
            );

            return JSON.parse(this.decoder.decode(decrypted));
            
        } catch (error) {
            console.error('解密失败:', error);
            // 解密失败时尝试解析为JSON
            try {
                return JSON.parse(encryptedData);
            } catch {
                return null;
            }
        }
    }

    /**
     * 简单哈希函数（用于数据完整性检查）
     */
    async hashData(data) {
        const encoded = this.encoder.encode(JSON.stringify(data));
        const hash = await window.crypto.subtle.digest('SHA-256', encoded);
        return btoa(String.fromCharCode(...new Uint8Array(hash)));
    }

    /**
     * 验证数据完整性
     */
    async verifyData(data, expectedHash) {
        const actualHash = await this.hashData(data);
        return actualHash === expectedHash;
    }

    /**
     * 安全的localStorage存储
     */
    async secureSetItem(key, data, password = 'default_password') {
        try {
            const encrypted = await this.encryptData(data, password);
            const hash = await this.hashData(data);
            
            localStorage.setItem(key, encrypted);
            localStorage.setItem(`${key}_hash`, hash);
            localStorage.setItem(`${key}_timestamp`, Date.now().toString());
            
            return true;
        } catch (error) {
            console.error('安全存储失败:', error);
            // 降级方案：普通存储
            localStorage.setItem(key, JSON.stringify(data));
            return false;
        }
    }

    /**
     * 安全的localStorage读取
     */
    async secureGetItem(key, password = 'default_password') {
        try {
            const encrypted = localStorage.getItem(key);
            if (!encrypted) return null;

            const data = await this.decryptData(encrypted, password);
            const expectedHash = localStorage.getItem(`${key}_hash`);
            
            if (expectedHash) {
                const isValid = await this.verifyData(data, expectedHash);
                if (!isValid) {
                    console.warn('数据完整性验证失败:', key);
                    return null;
                }
            }

            return data;
            
        } catch (error) {
            console.error('安全读取失败:', error);
            // 降级方案：普通读取
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : null;
            } catch {
                return null;
            }
        }
    }

    /**
     * 清除安全数据
     */
    secureRemoveItem(key) {
        localStorage.removeItem(key);
        localStorage.removeItem(`${key}_hash`);
        localStorage.removeItem(`${key}_timestamp`);
    }

    /**
     * 检查数据是否加密存储
     */
    isEncrypted(key) {
        const data = localStorage.getItem(key);
        if (!data) return false;
        
        // 简单的加密数据检测（Base64格式且长度较大）
        try {
            atob(data);
            return data.length > 100; // 加密数据通常较长
        } catch {
            return false;
        }
    }
}

// 创建全局实例
window.cryptoUtils = new CryptoUtils();

// 工具函数
async function encryptAllLocalStorage(password = 'default_password') {
    const crypto = new CryptoUtils();
    const keys = Object.keys(localStorage);
    
    for (const key of keys) {
        if (!key.includes('_hash') && !key.includes('_timestamp')) {
            try {
                const data = JSON.parse(localStorage.getItem(key));
                await crypto.secureSetItem(key, data, password);
            } catch (error) {
                console.warn(`加密失败 ${key}:`, error);
            }
        }
    }
    
    console.log('所有localStorage数据已加密');
}

async function decryptAllLocalStorage(password = 'default_password') {
    const crypto = new CryptoUtils();
    const keys = Object.keys(localStorage);
    
    for (const key of keys) {
        if (crypto.isEncrypted(key)) {
            try {
                const data = await crypto.secureGetItem(key, password);
                localStorage.setItem(key, JSON.stringify(data));
                localStorage.removeItem(`${key}_hash`);
                localStorage.removeItem(`${key}_timestamp`);
            } catch (error) {
                console.warn(`解密失败 ${key}:`, error);
            }
        }
    }
    
    console.log('所有localStorage数据已解密');
}