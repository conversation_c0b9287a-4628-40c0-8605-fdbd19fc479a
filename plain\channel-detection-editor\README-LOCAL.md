---
type: "manual"
---

# 渠道检测编辑器 - 纯本地存储版本

## 🚀 完全离线使用

这个版本提供完整的本地存储功能，无需任何服务器支持，双击 `index.html` 即可使用所有功能。

## ✨ 核心功能

### 1. 完整的持久化存储
- **规则管理**: 渠道检测规则的增删改查
- **提示词编辑**: 字段级提示词片段管理  
- **变更历史**: 完整的操作记录和版本跟踪
- **数据导出**: 一键备份所有配置数据
- **数据导入**: 从备份文件恢复数据

### 2. 高级功能
- **智能搜索**: 全文搜索所有规则和提示词
- **统计信息**: 实时数据统计和存储大小显示
- **批量操作**: 支持批量导入导出
- **数据清理**: 安全的数据清空功能

### 3. 用户体验
- **实时保存**: 所有操作立即持久化
- **错误恢复**: 自动数据恢复机制
- **状态显示**: 存储状态和统计信息
- **离线工作**: 完全不需要网络连接

## 📁 文件结构

```
channel-detection-editor/
├── index.html          # 主界面
├── local-storage-manager.js  # 本地存储核心
├── rule-editor.js      # 规则编辑器
├── prompt-editor.js    # 提示词编辑器
├── prompt-segmenter.js # 提示词分割工具
├── prompt-composer.js  # 提示词组合引擎
├── channel-detector.js # 渠道检测器
├── field-mapper.js     # 字段映射器
├── gemini-config.js    # Gemini配置
├── data.js            # 基础数据
└── config.js          # 应用配置
```

## 🎯 使用指南

### 快速开始
1. **双击** `index.html` 文件
2. 在输入框中粘贴订单内容
3. 点击"处理输入"查看渠道检测结果
4. 使用编辑功能管理规则和提示词

### 规则管理
- 点击"🛠️ 编辑规则"按钮
- 添加/删除渠道检测规则
- 设置置信度和匹配模式
- 自动保存到本地存储

### 提示词编辑  
- 点击"📝 编辑提示词"按钮
- 按字段管理提示词片段
- 支持默认值回退机制
- 实时预览和语法检查

### 数据管理
- **导出数据**: 备份所有配置到JSON文件
- **导入数据**: 从备份文件恢复配置
- **清空数据**: 重置为初始状态
- **查看统计**: 显示数据量和存储信息

## 🔧 技术特点

### 存储架构
```javascript
// 统一的数据结构
{
  channels: { /* 渠道配置 */ },
  rules: { /* 检测规则 */ },
  prompts: { /* 提示词片段 */ },
  history: [ /* 操作历史 */ ],
  lastUpdated: '2024-01-01T00:00:00Z'
}
```

### 性能优化
- **快速响应**: 所有操作优先内存处理
- **延迟持久化**: 批量操作优化性能
- **数据压缩**: 自动清理和历史限制
- **错误隔离**: 单点故障不影响整体

### 兼容性
- ✅ 所有现代浏览器
- ✅ 完全离线使用  
- ✅ 无外部依赖
- ✅ 移动端友好

## 💾 数据持久化

### 存储位置
- **主要存储**: localStorage (浏览器本地存储)
- **备份机制**: 导出/导入功能
- **恢复策略**: 自动数据验证和修复

### 数据安全
- **自动备份**: 操作前自动创建快照
- **版本控制**: 保留历史变更记录
- **错误恢复**: 数据损坏时自动恢复
- **容量管理**: 自动清理旧历史记录

## 🎨 界面功能

### 主界面
- 订单内容输入和实时处理
- 渠道检测结果可视化显示
- 字段映射卡片式展示
- 一键编辑入口

### 编辑器界面  
- 模态框形式的编辑界面
- 实时预览和语法高亮
- 字段级精细控制
- 默认值管理和回退

### 管理功能
- 数据统计和状态显示
- 搜索和过滤功能
- 批量操作支持
- 导入导出界面

## 📊 数据统计

系统提供完整的数据统计：
- 渠道数量、规则数量、提示词数量
- 历史记录条数、最后更新时间  
- 存储空间使用情况
- 操作频率和模式分析

## 🔄 升级路径

### 从旧版本升级
1. 导出旧数据备份
2. 打开新版本页面
3. 导入备份数据
4. 自动兼容性处理

### 未来扩展
- 可选的云端同步功能
- 团队协作支持
- 高级分析报表
- 插件系统扩展

这个版本提供了企业级的功能和可靠性，完全在浏览器中运行，无需任何服务器支持。