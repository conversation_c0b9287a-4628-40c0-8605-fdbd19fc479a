/**
 * 渠道配置模块 - 从config.js拆分
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（独立的渠道配置模块）
 * 被依赖：目前未被其他模块使用（备用模块）
 * 全局变量：创建 window.channelConfig 实例
 * 配置管理：渠道信息、角色权限、用户特定权限
 * 
 * === 核心功能 ===
 * - 渠道信息管理（增删改查）
 * - 角色权限模板（管理员/普通用户/只读用户）
 * - 用户特定权限覆盖
 * - 渠道访问权限检查
 * - 配置导入导出功能
 * 
 * === 集成点 ===
 * - 备用模块，可以替代 data.js 中的部分渠道配置
 * - 目前与主系统没有直接集成
 * - 可与 config.js 的权限系统互补
 * 
 * === 使用场景 ===
 * - 更细粒度的渠道权限管理
 * - 角色型权限控制系统
 * - 动态渠道配置管理
 * - 渠道信息的CRUD操作
 * 
 * === 注意事项 ===
 * 该模块与 data.js 中的渠道配置有重复，需谨慎使用
 * 主要用于更复杂的渠道管理需求
 * 角色权限系统可以与 config.js 的用户权限系统共存
 * 如果使用该模块，建议与主配置系统保持同步
 */

// 渠道配置模块 - 从config.js拆分

class ChannelConfig {
    constructor() {
        this.channels = this.loadChannels();
        this.permissions = this.loadPermissions();
    }

    /**
     * 加载渠道数据
     */
    loadChannels() {
        return [
            { id: 'fliggy', name: 'Fliggy', displayName: '飞猪', description: '阿里巴巴旗下旅游平台' },
            { id: 'jingge', name: 'JingGe', displayName: '京鸽', description: '京东旅行渠道' },
            { id: 'ctrip', name: 'Ctrip', displayName: '携程', description: '中国领先的在线旅行服务公司' },
            { id: 'qunar', name: 'Qunar', displayName: '去哪儿', description: '在线旅行预订平台' },
            { id: 'meituan', name: 'Meituan', displayName: '美团', description: '生活服务电子商务平台' },
            { id: 'tuniu', name: 'Tuniu', displayName: '途牛', description: '在线休闲旅游公司' },
            { id: 'mangocity', name: 'Mangocity', displayName: '芒果网', description: '在线旅游服务提供商' },
            { id: 'elong', name: 'Elong', displayName: '艺龙', description: '在线旅行服务提供商' },
            { id: 'tongcheng', name: 'Tongcheng', displayName: '同程', description: '在线旅行服务公司' },
            { id: 'lvmama', name: 'Lvmama', displayName: '驴妈妈', description: '在线旅游服务网站' }
        ];
    }

    /**
     * 加载权限配置
     */
    loadPermissions() {
        return {
            // 角色权限模板
            roleTemplates: {
                // 管理员权限
                1: {
                    channels: { 
                        access: 'all',
                        management: 'all',
                        pricing: 'all'
                    },
                    features: {
                        user_management: true,
                        config_management: true,
                        data_export: true,
                        system_settings: true
                    }
                },
                
                // 普通用户权限
                2: {
                    channels: {
                        access: ['fliggy', 'jingge', 'ctrip'],
                        management: 'none',
                        pricing: 'view_only'
                    },
                    features: {
                        user_management: false,
                        config_management: false,
                        data_export: true,
                        system_settings: false
                    }
                },
                
                // 只读用户权限
                3: {
                    channels: {
                        access: ['fliggy', 'jingge'],
                        management: 'none',
                        pricing: 'view_only'
                    },
                    features: {
                        user_management: false,
                        config_management: false,
                        data_export: false,
                        system_settings: false
                    }
                }
            },
            
            // 用户特定权限覆盖
            userOverrides: {
                // 示例：用户37有特殊权限
                37: {
                    channels: {
                        access: 'all',
                        management: ['fliggy', 'jingge'],
                        pricing: 'all'
                    }
                }
            }
        };
    }

    /**
     * 获取所有渠道
     */
    getAllChannels() {
        return this.channels;
    }

    /**
     * 根据ID查找渠道
     */
    findChannelById(id) {
        return this.channels.find(channel => channel.id === id);
    }

    /**
     * 根据名称查找渠道
     */
    findChannelByName(name) {
        return this.channels.find(channel => 
            channel.name.toLowerCase().includes(name.toLowerCase()) ||
            channel.displayName.toLowerCase().includes(name.toLowerCase())
        );
    }

    /**
     * 获取用户权限
     */
    getUserPermissions(userId, roleId = 2) {
        const template = this.permissions.roleTemplates[roleId] || this.permissions.roleTemplates[2];
        const userOverride = this.permissions.userOverrides[userId];
        
        // 合并模板和用户特定权限
        return userOverride ? { ...template, ...userOverride } : template;
    }

    /**
     * 检查用户权限
     */
    hasPermission(userId, permissionPath, roleId = 2) {
        const permissions = this.getUserPermissions(userId, roleId);
        const pathParts = permissionPath.split('.');
        let value = permissions;
        
        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return false;
            }
        }
        
        return value !== false && value !== undefined && value !== null;
    }

    /**
     * 获取用户可访问的渠道
     */
    getAllowedChannels(userId, roleId = 2) {
        const permissions = this.getUserPermissions(userId, roleId);
        
        if (permissions.channels.access === 'all') {
            return this.channels.map(ch => ch.id);
        }
        
        if (Array.isArray(permissions.channels.access)) {
            return permissions.channels.access;
        }
        
        return [];
    }

    /**
     * 获取用户默认渠道
     */
    getDefaultChannel(userId, roleId = 2) {
        const allowedChannels = this.getAllowedChannels(userId, roleId);
        return allowedChannels.length > 0 ? allowedChannels[0] : null;
    }

    /**
     * 添加新渠道
     */
    addChannel(channel) {
        // 检查是否已存在
        if (this.channels.find(c => c.id === channel.id)) {
            throw new Error(`渠道ID ${channel.id} 已存在`);
        }
        
        this.channels.push(channel);
        return channel;
    }

    /**
     * 更新渠道
     */
    updateChannel(id, updates) {
        const channelIndex = this.channels.findIndex(ch => ch.id === id);
        if (channelIndex !== -1) {
            this.channels[channelIndex] = { ...this.channels[channelIndex], ...updates };
            return this.channels[channelIndex];
        }
        return null;
    }

    /**
     * 删除渠道
     */
    deleteChannel(id) {
        const channelIndex = this.channels.findIndex(ch => ch.id === id);
        if (channelIndex !== -1) {
            return this.channels.splice(channelIndex, 1)[0];
        }
        return null;
    }

    /**
     * 设置用户权限覆盖
     */
    setUserOverride(userId, override) {
        this.permissions.userOverrides[userId] = override;
    }

    /**
     * 移除用户权限覆盖
     */
    removeUserOverride(userId) {
        delete this.permissions.userOverrides[userId];
    }

    /**
     * 导出配置
     */
    exportConfig() {
        return JSON.stringify({
            channels: this.channels,
            permissions: this.permissions
        }, null, 2);
    }

    /**
     * 导入配置
     */
    importConfig(jsonData) {
        try {
            const config = JSON.parse(jsonData);
            if (config.channels) this.channels = config.channels;
            if (config.permissions) this.permissions = config.permissions;
            return true;
        } catch (error) {
            console.error('导入配置失败:', error);
            return false;
        }
    }
}

// 创建全局实例
window.channelConfig = new ChannelConfig();