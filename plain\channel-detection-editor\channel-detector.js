/**
 * 渠道检测器模块 - 模块化重构版本
 * 
 * 重构更新：
 * - 消除全局变量污染 (window.channelDetector)  
 * - 移除对data.js的隐式全局依赖
 * - 实现依赖注入模式，通过构造函数传入规则配置
 * - 保持检测逻辑和API接口的稳定性
 * 
 * 设计原则：
 * - 依赖显式化：通过参数明确传入检测规则
 * - 单一职责：专注于渠道检测逻辑  
 * - 可测试性：支持模拟规则配置的单元测试
 * - 向后兼容：保持原有检测API不变
 */
class ChannelDetector {
    constructor(detectionRulesConfig = null, dataUtils = null) {
        // 依赖注入 - 通过参数接收配置和工具
        this.dataUtils = dataUtils || this.getDefaultDataUtils();
        
        if (detectionRulesConfig) {
            this.initializeWithConfig(detectionRulesConfig);
        } else {
            // 向后兼容 - 尝试使用全局配置
            this.initializeWithGlobalConfig();
        }
        
        console.log('渠道检测器已初始化 - 模块化版本');
    }
    
    /**
     * 使用传入的配置初始化
     */
    initializeWithConfig(config) {
        this.detectionRules = this.processDetectionRules(config);
    }
    
    /**
     * 向后兼容 - 使用全局配置初始化
     */
    initializeWithGlobalConfig() {
        if (typeof window !== 'undefined' && 
            window.CHANNEL_DETECTION_RULES && 
            window.REFERENCE_PATTERNS && 
            window.KEYWORD_DETECTION) {
            
            const config = {
                channelRules: window.CHANNEL_DETECTION_RULES,
                referencePatterns: window.REFERENCE_PATTERNS,
                keywordDetection: window.KEYWORD_DETECTION
            };
            
            this.initializeWithConfig(config);
            console.log('✅ 使用全局配置初始化（兼容模式）');
        } else {
            // 使用默认配置
            this.initializeWithDefaultConfig();
            console.log('⚠️  使用默认配置初始化');
        }
    }
    
    /**
     * 处理检测规则配置
     */
    processDetectionRules(config) {
        const rules = {};
        
        // 处理渠道检测规则
        if (config.channelRules) {
            for (const [key, rule] of Object.entries(config.channelRules)) {
                rules[key] = {
                    patterns: rule.patterns.map(pattern => this.dataUtils.patternToRegex(pattern)),
                    confidence: rule.confidence,
                    channel: rule.name
                };
            }
        }
        
        // 添加参考号和关键词检测
        rules.referencePatterns = config.referencePatterns || {};
        rules.keywordPatterns = config.keywordDetection || {};
        
        return rules;
    }
    
    /**
     * 使用默认配置初始化
     */
    initializeWithDefaultConfig() {
        this.detectionRules = {
            // 基本的Fliggy检测规则
            fliggy: {
                patterns: [/订单编号.*\d{19}/i, /fliggy/i].map(p => p),
                confidence: 0.9,
                channel: 'Fliggy'
            },
            
            // 基本的参考号检测
            referencePatterns: {
                'CD': { channel: 'Ctrip West Malaysia', confidence: 0.8 },
                'CT': { channel: 'Ctrip API', confidence: 0.8 },
                'KL': { channel: 'Klook West Malaysia', confidence: 0.8 },
                'KK': { channel: 'Kkday', confidence: 0.8 }
            },
            
            // 基本的关键词检测
            keywordPatterns: {
                'klook': { channel: 'Klook West Malaysia', confidence: 0.7 },
                'kkday': { channel: 'Kkday', confidence: 0.7 },
                'ctrip': { channel: 'Ctrip West Malaysia', confidence: 0.7 }
            }
        };
    }
    
    /**
     * 获取默认数据工具
     */
    getDefaultDataUtils() {
        return {
            patternToRegex: (pattern) => {
                if (pattern instanceof RegExp) return pattern;
                if (typeof pattern === 'string') {
                    try {
                        return new RegExp(pattern, 'i');
                    } catch (e) {
                        return new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
                    }
                }
                return /./;
            }
        };
    }

    /**
     * 检测渠道
     */
    detectChannel(input) {
        try {
            if (!input || typeof input !== 'string') {
                return { channel: null, confidence: 0, method: 'invalid_input' };
            }

            console.log('开始渠道检测', { inputLength: input.length });

            // 1. Fliggy渠道检测
            const fliggyResult = this.detectFliggy(input);
            if (fliggyResult.confidence > 0.8) {
                console.log('检测到Fliggy渠道', fliggyResult);
                return fliggyResult;
            }

            // 2. JingGe渠道检测
            const jinggeResult = this.detectJingGe(input);
            if (jinggeResult.confidence > 0.8) {
                console.log('检测到JingGe渠道', jinggeResult);
                return jinggeResult;
            }

            // 3. 参考号模式检测
            const referenceResult = this.detectByReference(input);
            if (referenceResult.confidence > 0.8) {
                console.log('通过参考号检测到渠道', referenceResult);
                return referenceResult;
            }

            // 4. 关键词检测
            const keywordResult = this.detectByKeywords(input);
            if (keywordResult.confidence > 0.6) {
                console.log('通过关键词检测到渠道', keywordResult);
                return keywordResult;
            }

            // 未检测到任何渠道
            console.log('未检测到特定渠道');
            return { channel: null, confidence: 0, method: 'no_match' };

        } catch (error) {
            console.error('渠道检测失败:', error);
            return { channel: null, confidence: 0, method: 'error', error: error.message };
        }
    }

    /**
     * 检测Fliggy渠道
     */
    detectFliggy(text) {
        const rules = this.detectionRules.fliggy;
        let maxConfidence = 0;
        let matchedPattern = null;

        for (const pattern of rules.patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                if (pattern.source.includes('订单编号')) {
                    maxConfidence = 0.95;
                    matchedPattern = '订单编号+19位数字';
                    break;
                } else {
                    maxConfidence = Math.max(maxConfidence, 0.85);
                    matchedPattern = pattern.source;
                }
            }
        }

        return {
            channel: maxConfidence > 0 ? rules.channel : null,
            confidence: maxConfidence,
            method: 'fliggy_pattern',
            matchedPattern
        };
    }

    /**
     * 检测JingGe渠道
     */
    detectJingGe(text) {
        const rules = this.detectionRules.jingge;
        let maxConfidence = 0;
        let matchedPattern = null;

        for (const pattern of rules.patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                maxConfidence = rules.confidence;
                matchedPattern = pattern.source;
                break;
            }
        }

        return {
            channel: maxConfidence > 0 ? rules.channel : null,
            confidence: maxConfidence,
            method: 'jingge_pattern',
            matchedPattern
        };
    }

    /**
     * 基于参考号检测
     */
    detectByReference(text) {
        const referencePattern = /\b([A-Z]{2})[A-Z0-9]{6,12}\b/g;
        const matches = text.match(referencePattern);

        if (matches && matches.length > 0) {
            for (const match of matches) {
                const prefix = match.substring(0, 2);
                const rule = this.detectionRules.referencePatterns[prefix];
                
                if (rule) {
                    return {
                        channel: rule.channel,
                        confidence: rule.confidence,
                        method: 'reference_pattern',
                        matchedReference: match
                    };
                }
            }
        }

        return { channel: null, confidence: 0, method: 'reference_no_match' };
    }

    /**
     * 基于关键词检测
     */
    detectByKeywords(text) {
        const lowerText = text.toLowerCase();

        for (const [keyword, rule] of Object.entries(this.detectionRules.keywordPatterns || {})) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return {
                    channel: rule.channel,
                    confidence: rule.confidence,
                    method: 'keyword_match',
                    matchedKeyword: keyword
                };
            }
        }

        return { channel: null, confidence: 0, method: 'keyword_no_match' };
    }

    /**
     * 获取所有检测规则（用于编辑）
     * 将 RegExp 转换为其源字符串，避免 JSON 序列化丢失
     */
    getDetectionRules() {
        const clone = {};
        for (const [key, rule] of Object.entries(this.detectionRules)) {
            if (key === 'referencePatterns' || key === 'keywordPatterns') {
                clone[key] = rule; // 直接复制
                continue;
            }
            if (!rule || !Array.isArray(rule.patterns)) continue;
            clone[key] = {
                channel: rule.channel,
                confidence: rule.confidence,
                patterns: rule.patterns.map(p => p instanceof RegExp ? p.source : (typeof p === 'string' ? p : ''))
            };
        }
        return clone;
    }

    /**
     * 更新检测规则
     * 将字符串模式转换回 RegExp
     */
    updateDetectionRules(newRules) {
        for (const [key, rule] of Object.entries(newRules)) {
            if (key === 'referencePatterns' || key === 'keywordPatterns') {
                this.detectionRules[key] = rule; // 原样覆盖
                continue;
            }
            if (!rule) continue;
            const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
            this.detectionRules[key] = {
                channel: rule.channel || this.detectionRules[key]?.channel || key,
                confidence: typeof rule.confidence === 'number' ? rule.confidence : (this.detectionRules[key]?.confidence || 0.8),
                patterns: patterns
                    .map(p => {
                        try {
                            if (p instanceof RegExp) return p;
                            if (typeof p === 'string') return this.dataUtils.patternToRegex(p);
                            return null;
                        } catch { return null; }
                    })
                    .filter(Boolean)
            };
        }
        console.log('检测规则已更新', this.detectionRules);
        return true;
    }

    /**
     * 添加新的渠道检测规则
     */
    addChannelRule(channelName, patterns, confidence = 0.8) {
        this.detectionRules[channelName.toLowerCase()] = {
            patterns: patterns.map(p => new RegExp(p)),
            confidence: confidence,
            channel: channelName
        };
        console.log(`已添加渠道规则: ${channelName}`);
        return true;
    }
}

// 模块工厂函数 - 替代全局实例创建  
function createChannelDetectorModule(container) {
    // 从容器中获取数据配置（如果可用）
    let dataConfig = null;
    let dataUtils = null;
    
    try {
        // 尝试从数据模块获取配置
        if (container && container.has('data')) {
            const dataModule = container.get('data');
            dataConfig = {
                channelRules: dataModule.CHANNEL_DETECTION_RULES,
                referencePatterns: dataModule.REFERENCE_PATTERNS,
                keywordDetection: dataModule.KEYWORD_DETECTION
            };
            dataUtils = dataModule.DataUtils;
        }
    } catch (error) {
        console.warn('⚠️  无法获取数据模块，使用默认配置');
    }
    
    return new ChannelDetector(dataConfig, dataUtils);
}

// 向后兼容支持 - 为现有代码提供过渡期
if (typeof window !== 'undefined' && !window.moduleContainer) {
    console.warn('⚠️  检测到传统模式，创建ChannelDetector兼容实例');
    window.channelDetector = new ChannelDetector();
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('channelDetector', createChannelDetectorModule, ['data']);
    console.log('📦 ChannelDetector已注册到模块容器');
}