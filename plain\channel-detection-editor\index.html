<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道检测编辑器 - 独立项目</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .input-section textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', monospace;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-section textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .result-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow: auto;
            border: 1px solid #e9ecef;
        }

        .result-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
            border-left: 4px solid #4facfe;
        }

        .result-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .result-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .result-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .field-mapping {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .field-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .field-card h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .field-card p {
            color: #6c757d;
            font-size: 13px;
            margin: 0;
        }

        .field-value {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            margin-top: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #495057;
            border: 1px solid #e9ecef;
            min-height: 20px;
        }

        .field-value:not(:empty) {
            background: #e8f5e8;
            border-color: #28a745;
            color: #155724;
        }

        .channel-detection {
            margin-top: 20px;
        }

        .detection-result {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4facfe;
            margin-top: 15px;
        }

        .detection-result h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .detection-details {
            font-size: 13px;
            color: #495057;
        }

        .confidence-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .field-mapping {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 渠道检测编辑器</h1>
            <p>独立项目 - 输入内容映射到表单 + 渠道检测特征编辑</p>
        </div>

        <div class="main-content">
            <div class="section input-section">
            <h2>📝 输入内容</h2>
            <textarea id="inputContent" placeholder="请输入订单内容..."></textarea>
                
                <div class="button-group">
                    <button class="btn-primary" onclick="processInput()">处理输入</button>
                    <button class="btn-secondary" onclick="clearInput()">清空</button>
                    <button class="btn-secondary" onclick="openRuleEditor()" style="background: #fd7e14;">🛠️ 编辑规则</button>
                    <button class="btn-secondary" onclick="openPromptEditor()" style="background: #6f42c1;">📝 编辑提示词</button>
                </div>
            </div>

            <div class="section">
                <h2>📊 处理结果</h2>
                <div class="result-section" id="resultContainer">
                    <div class="result-item">
                        <strong>等待处理...</strong>
                        <p>请输入内容并点击"处理输入"按钮</p>
                    </div>
                </div>

                <div class="channel-detection">
                    <h3>🔍 渠道检测结果</h3>
                    <div class="detection-result" id="channelResult">
                        <h4>未检测</h4>
                        <p>等待渠道检测...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" style="grid-column: 1 / -1; margin: 30px;">
            <h2>🗂️ 字段映射展示</h2>
            <div class="field-mapping" id="fieldMapping">
                <div class="field-card">
                    <h4>customer_name</h4>
                    <p>客户姓名</p>
                    <div class="field-value" id="customer_name_value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_contact</h4>
                    <p>客户联系电话</p>
                    <div class="field-value" id="customer_contact_value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_email</h4>
                    <p>客户邮箱</p>
                    <div class="field-value" id="customer_email_value">-</div>
                </div>
                <div class="field-card">
                    <h4>ota_reference_number</h4>
                    <p>OTA参考编号</p>
                    <div class="field-value" id="ota_reference_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>flight_info</h4>
                    <p>航班信息</p>
                    <div class="field-value" id="flight_info_value">-</div>
                </div>
                <div class="field-card">
                    <h4>pickup</h4>
                    <p>接客地点</p>
                    <div class="field-value" id="pickup_value">-</div>
                </div>
                <div class="field-card">
                    <h4>destination</h4>
                    <p>目的地</p>
                    <div class="field-value" id="destination_value">-</div>
                </div>
                <div class="field-card">
                    <h4>passenger_number</h4>
                    <p>乘客数量</p>
                    <div class="field-value" id="passenger_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>luggage_number</h4>
                    <p>行李数量</p>
                    <div class="field-value" id="luggage_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>ota_price</h4>
                    <p>OTA平台价格</p>
                    <div class="field-value" id="ota_price_value">-</div>
                </div>
                <div class="field-card">
                    <h4>sub_category_id</h4>
                    <p>服务类型ID (2:接机, 3:送机, 4:包车)</p>
                    <div class="field-value" id="sub_category_id_value">-</div>
                </div>
                <div class="field-card">
                    <h4>extra_requirement</h4>
                    <p>额外要求/备注信息</p>
                    <div class="field-value" id="extra_requirement_value">-</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 重构版本模块加载顺序 -->
    
    <!-- 1. 模块容器系统 (最先加载) -->
    <script src="module-container.js"></script>
    
    <!-- 2. 缓存系统 (核心性能优化) -->
    <script src="cache-manager.js"></script>
    <script src="cache-integration-adapter.js"></script>
    <script src="cache-monitor-panel.js"></script>
    
    <!-- 3. 错误处理和核心工具 -->
    <script src="error-handler.js"></script>
    <script src="crypto-utils.js"></script>
    <script src="local-storage-manager.js"></script>
    
    <!-- 4. 数据层和配置 (注册为模块) -->
    <script src="data.js"></script>
    <script src="../hotels_by_region.js"></script>
    <script src="airport-data.js"></script>
    <script src="config.js"></script>
    <script src="gemini-config.js"></script>
    
    <!-- 5. 业务模块 (按依赖顺序注册) -->
    <script src="prompt-segmenter.js"></script>
    <script src="prompt-composer.js"></script>
    <script src="address-translator.js"></script>
    <script src="channel-detector.js"></script>
    <script src="field-mapper.js"></script>
    <script src="rule-editor.js"></script>
    <script src="prompt-editor.js"></script>
    
    <!-- 6. 主应用逻辑 (最后加载，作为容器入口) -->
    <script src="app.js"></script>
    
    <!-- 7. 缓存系统初始化 -->
    <script>
        // 缓存系统初始化脚本
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 初始化缓存系统...');
            
            try {
                // 等待模块容器初始化完成
                if (window.moduleContainer) {
                    await window.moduleContainer.initialize();
                    
                    // 获取缓存集成适配器
                    const cacheAdapter = window.moduleContainer.get('cacheIntegrationAdapter');
                    
                    if (cacheAdapter) {
                        // 为现有模块添加缓存增强
                        const fieldMapper = window.moduleContainer.get('fieldMapper');
                        const addressTranslator = window.moduleContainer.get('addressTranslator');
                        const channelDetector = window.moduleContainer.get('channelDetector');
                        
                        if (fieldMapper) {
                            cacheAdapter.enhanceFieldMapper(fieldMapper);
                            console.log('✅ FieldMapper缓存增强已启用');
                        }
                        
                        if (addressTranslator) {
                            cacheAdapter.enhanceAddressTranslator(addressTranslator);
                            console.log('✅ AddressTranslator缓存增强已启用');
                        }
                        
                        if (channelDetector) {
                            cacheAdapter.enhanceChannelDetector(channelDetector);
                            console.log('✅ ChannelDetector缓存增强已启用');
                        }
                        
                        // 启动缓存监控面板
                        const monitorPanel = window.moduleContainer.get('cacheMonitorPanel');
                        if (monitorPanel) {
                            console.log('📊 缓存监控面板已就绪');
                        }
                        
                        console.log('🎉 缓存系统初始化完成');
                        
                        // 显示缓存系统提示
                        setTimeout(() => {
                            const notification = document.createElement('div');
                            notification.style.cssText = `
                                position: fixed;
                                top: 20px;
                                right: 20px;
                                background: #10b981;
                                color: white;
                                padding: 12px 16px;
                                border-radius: 8px;
                                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                                z-index: 9999;
                                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                font-size: 14px;
                                max-width: 300px;
                                opacity: 0;
                                transform: translateY(-20px);
                                transition: all 0.3s ease;
                            `;
                            notification.innerHTML = `
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span>🚀</span>
                                    <div>
                                        <div style="font-weight: 600;">缓存系统已启用</div>
                                        <div style="font-size: 12px; opacity: 0.9; margin-top: 2px;">AI调用和地址翻译将被自动缓存，点击右下角图标查看监控面板</div>
                                    </div>
                                </div>
                            `;
                            
                            document.body.appendChild(notification);
                            
                            setTimeout(() => {
                                notification.style.opacity = '1';
                                notification.style.transform = 'translateY(0)';
                            }, 100);
                            
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                notification.style.transform = 'translateY(-20px)';
                                setTimeout(() => notification.remove(), 300);
                            }, 5000);
                        }, 1000);
                        
                    } else {
                        console.warn('⚠️ 缓存集成适配器不可用');
                    }
                    
                } else {
                    console.warn('⚠️ 模块容器不可用，缓存系统将在传统模式下运行');
                    
                    // 传统模式下的缓存集成
                    if (window.cacheIntegrationAdapter) {
                        if (window.fieldMapper) {
                            window.cacheIntegrationAdapter.enhanceFieldMapper(window.fieldMapper);
                        }
                        
                        if (window.addressTranslator) {
                            window.cacheIntegrationAdapter.enhanceAddressTranslator(window.addressTranslator);
                        }
                        
                        if (window.channelDetector) {
                            window.cacheIntegrationAdapter.enhanceChannelDetector(window.channelDetector);
                        }
                        
                        console.log('✅ 传统模式缓存集成完成');
                    }
                }
                
            } catch (error) {
                console.error('❌ 缓存系统初始化失败:', error);
            }
        });
    </script>
    
    <!-- 8. 传统脚本依然存在的兼容性支持说明 -->
    <!-- 现有脚本会自动检测模块容器存在性，优先使用新架构，降级到传统模式 -->
    <!-- 缓存系统也支持同样的向后兼容机制 -->
    
    <!-- 移除不存在的文件 -->
    <!-- <script src="config-demo.js"></script> -->
    <!-- <script src="gemini-demo.js"></script> -->
</body>
</html>