/**
 * 渠道检测规则编辑器模块
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：channel-detector.js（检测规则）, local-storage-manager.js（规则存储）
 * 被依赖：app.js（规则编辑界面）, prompt-editor.js（渠道同步）
 * 全局变量：创建 RuleEditor 实例（非全局）
 * 规则管理：渠道规则编辑、测试、保存、导入导出
 * 
 * === 核心功能 ===
 * - 渠道检测规则的图形化编辑界面
 * - 规则测试和验证功能
 * - 规则导入导出（JSON格式）
 * - 本地存储和恢复规则配置
 * - 规则冲突检测和解决
 * 
 * === 集成点 ===
 * - app.js：提供规则编辑界面入口
 * - channel-detector.js：获取和更新检测规则
 * - local-storage-manager.js：保存和加载规则配置
 * - prompt-editor.js：同步渠道列表用于提示词编辑
 * 
 * === 使用场景 ===
 * - 渠道检测规则的可视化编辑和管理
 * - 规则效果的实时测试和验证
 * - 规则配置的备份和恢复
 * - 多规则冲突检测和优先级设置
 * 
 * === 注意事项 ===
 * 使用模态框界面进行规则编辑
 * 支持实时规则测试和效果预览
 * 规则保存到本地存储，支持多版本
 * 与channel-detector紧密集成，确保规则一致性
 */
class RuleEditor {
    constructor() {
        this.currentRules = null;
        this.localStorageManager = null;
        this.initializeEditor();
    }

    async initializeEditor() {
        console.log('规则编辑器已初始化');
        
        // 初始化本地存储管理器引用
        this.localStorageManager = window.localStorageManager;
        
        // 页面加载时恢复保存的规则
        await this.loadPersistedRules();
    }

    /**
     * 从持久化存储加载规则并更新检测器
     */
    async loadPersistedRules() {
        try {
            const savedRules = await this.loadRulesFromStorage();
            
            if (savedRules && Object.keys(savedRules).length > 0) {
                console.log('✅ 已加载保存的渠道规则', Object.keys(savedRules));
                
                // 更新当前规则
                this.currentRules = savedRules;
                
                // 更新全局检测器
                if (window.channelDetector) {
                    window.channelDetector.updateDetectionRules(savedRules);
                }
                
                // 发布渠道更新事件
                this.publishChannelsUpdated();
                
                return true;
            } else {
                console.log('📝 未找到保存的规则，使用默认规则');
                // 使用默认规则
                this.currentRules = window.channelDetector?.getDetectionRules() || {};
                return false;
            }
        } catch (error) {
            console.error('❌ 加载规则失败:', error);
            this.currentRules = window.channelDetector?.getDetectionRules() || {};
            return false;
        }
    }

    /**
     * 打开规则编辑器
     */
    async openEditor() {
        try {
            // 使用当前已加载的规则，如果没有则从检测器获取
            if (!this.currentRules) {
                this.currentRules = window.channelDetector?.getDetectionRules() || {};
            }
            
            console.log('📝 打开规则编辑器，当前规则:', Object.keys(this.currentRules));
            this.createEditorModal();
        } catch (error) {
            console.error('❌ 打开规则编辑器失败:', error);
            alert('打开编辑器失败: ' + error.message);
        }
    }

    /**
     * 创建编辑器模态框
     */
    createEditorModal() {
        try {
            // 移除现有的编辑器（如果存在）
            this.closeEditor();

            const modal = document.createElement('div');
            modal.id = 'rule-editor-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 30px;
                border-radius: 12px;
                width: 90%;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
            `;

            content.innerHTML = this.getEditorHTML();
            modal.appendChild(content);
            document.body.appendChild(modal);

            // 绑定事件
            this.bindEditorEvents();

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeEditor();
                }
            });
            
            console.log('✅ 规则编辑器模态框已创建');
        } catch (error) {
            console.error('❌ 创建编辑器模态框失败:', error);
            throw new Error('创建编辑器界面失败: ' + error.message);
        }
    }

    /**
     * 获取编辑器HTML
     */
    getEditorHTML() {
        return `
            <div style="margin-bottom: 20px;">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">🛠️ 渠道检测规则编辑器</h2>
                
                <div style="margin-bottom: 20px;">
                    <button onclick="ruleEditor.addNewChannel()" style="
                        padding: 10px 20px;
                        background: #28a745;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        margin-right: 10px;
                    ">➕ 添加新渠道</button>
                    
                    <button onclick="ruleEditor.closeEditor()" style="
                        padding: 10px 20px;
                        background: #6c757d;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                    ">❌ 关闭</button>
                </div>

                <div id="channel-rules-container">
                    ${this.renderChannelRules()}
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <button onclick="ruleEditor.saveRules()" style="
                        padding: 12px 30px;
                        background: #007bff;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: bold;
                    ">💾 保存规则</button>
                    
                    <button onclick="ruleEditor.testRules()" style="
                        padding: 12px 30px;
                        background: #17a2b8;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        margin-left: 10px;
                    ">🧪 测试规则</button>
                    
                    <button onclick="ruleEditor.exportRules()" style="
                        padding: 12px 30px;
                        background: #6f42c1;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        margin-left: 10px;
                    ">📤 导出规则</button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染渠道规则
     */
    renderChannelRules() {
        let html = '';
        
        // 确保 currentRules 存在且是对象
        if (!this.currentRules || typeof this.currentRules !== 'object') {
            return '<div style="padding: 20px; text-align: center; color: #6c757d;">暂无规则数据</div>';
        }
        
        Object.entries(this.currentRules).forEach(([channelKey, rule]) => {
            if (channelKey === 'referencePatterns') {
                html += this.renderReferencePatterns(rule);
            } else if (channelKey === 'keywordPatterns') {
                // 跳过 keywordPatterns，因为它是系统内部使用的
                return;
            } else {
                html += this.renderSingleChannel(channelKey, rule);
            }
        });

        return html || '<div style="padding: 20px; text-align: center; color: #6c757d;">暂无渠道规则</div>';
    }

    /**
     * 渲染单个渠道规则
     */
    renderSingleChannel(channelKey, rule) {
        // 确保 rule 对象有默认值
        const safeRule = {
            channel: rule?.channel || channelKey || '',
            confidence: rule?.confidence ?? 0.8,
            patterns: rule?.patterns || []
        };

        // 将正则表达式或字符串模式转换回纯文字模式
        const patterns = (Array.isArray(safeRule.patterns) ? safeRule.patterns : [])
            .map(pattern => {
                // 支持两种形态：RegExp 或 源字符串
                const source = pattern instanceof RegExp ? pattern.source : String(pattern || '');
                // 特殊处理：19位数字模式
                if (source.includes('订单编号') && source.includes('\\d{19}')) {
                    return '订单编号：19位数字';
                }
                // 普通正则转纯文字（如果是字符串且非正则源，也直接返回）
                if (!(pattern instanceof RegExp)) return source;
                return source
                    .replace(/\\\(/g, '(')
                    .replace(/\\\)/g, ')')
                    .replace(/\\\[/g, '[')
                    .replace(/\\\]/g, ']')
                    .replace(/\\\*/g, '*')
                    .replace(/\\\?/g, '?')
                    .replace(/\\\+/g, '+')
                    .replace(/\\\^/g, '^')
                    .replace(/\\\$/g, '$')
                    .replace(/\\\|/g, '|');
            })
            .join('\n');
        
        return `
            <div style="
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                border: 1px solid #e9ecef;
            ">
                <h3 style="color: #495057; margin-bottom: 15px;">
                    📊 ${safeRule.channel} (${channelKey})
                    <button onclick="ruleEditor.deleteChannel('${channelKey}')" style="
                        float: right;
                        padding: 5px 10px;
                        background: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">🗑️ 删除</button>
                </h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            渠道名称:
                        </label>
                        <input type="text" 
                               value="${safeRule.channel}" 
                               data-channel="${channelKey}" 
                               data-field="channel"
                               style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                    </div>
                    
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            置信度:
                        </label>
                        <input type="number" 
                               value="${safeRule.confidence}" 
                               min="0" max="1" step="0.05"
                               data-channel="${channelKey}" 
                               data-field="confidence"
                               style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                    </div>
                </div>
                
                <div style="margin-top: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                        检测模式 (每行一个纯文字模式):
                    </label>
                    <textarea 
                        data-channel="${channelKey}" 
                        data-field="patterns"
                        style="width: 100%; min-height: 100px; padding: 10px; border: 1px solid #ced4da; border-radius: 4px; font-family: monospace;"
                    >${patterns}</textarea>
                    <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                        提示: 使用纯文字模式，如 "订单编号：19位数字" 或 "携程"
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染参考号模式
     */
    renderReferencePatterns(referencePatterns) {
        let html = `
            <div style="
                background: #e8f4fd;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                border: 1px solid #b8d4fd;
            ">
                <h3 style="color: #0c63e4; margin-bottom: 15px;">🔤 参考号模式检测</h3>
        `;

        Object.entries(referencePatterns).forEach(([prefix, rule]) => {
            html += `
                <div style="
                    background: white;
                    padding: 15px;
                    border-radius: 6px;
                    margin-bottom: 10px;
                    border: 1px solid #dee2e6;
                ">
                    <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 10px; align-items: center;">
                        <div>
                            <strong>前缀:</strong> ${prefix}
                        </div>
                        <div>
                            <input type="text" 
                                   value="${rule.channel}" 
                                   data-ref-prefix="${prefix}" 
                                   data-field="channel"
                                   placeholder="渠道名称"
                                   style="width: 100%; padding: 6px; border: 1px solid #ced4da; border-radius: 4px;">
                        </div>
                        <div>
                            <input type="number" 
                                   value="${rule.confidence}" 
                                   min="0" max="1" step="0.05"
                                   data-ref-prefix="${prefix}" 
                                   data-field="confidence"
                                   placeholder="置信度"
                                   style="width: 100%; padding: 6px; border: 1px solid #ced4da; border-radius: 4px;">
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
            <button onclick="ruleEditor.addReferencePattern()" style="
                padding: 8px 16px;
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">➕ 添加参考号模式</button>
            </div>
        `;

        return html;
    }

    /**
     * 绑定编辑器事件
     */
    bindEditorEvents() {
        // 实时保存输入变化
        const inputs = document.querySelectorAll('#rule-editor-modal input, #rule-editor-modal textarea');
        inputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.updateRuleFromInput(e.target);
            });
        });
    }

    /**
     * 从输入更新规则
     */
    updateRuleFromInput(input) {
        const channelKey = input.dataset.channel;
        const refPrefix = input.dataset.refPrefix;
        const field = input.dataset.field;
        const value = input.value;

        if (channelKey) {
            if (field === 'patterns') {
                // 处理模式数组 - 将纯文字转换为正则表达式
                const patterns = value.split('\n').filter(p => p.trim()).map(p => {
                    try {
                        return DataUtils.patternToRegex(p);
                    } catch (e) {
                        console.warn('无效的模式:', p, e);
                        return null;
                    }
                }).filter(p => p !== null);

                this.currentRules[channelKey].patterns = patterns;
            } else {
                this.currentRules[channelKey][field] = field === 'confidence' ? parseFloat(value) : value;
            }
        } else if (refPrefix) {
            this.currentRules.referencePatterns[refPrefix][field] = 
                field === 'confidence' ? parseFloat(value) : value;
        }
    }

    /**
     * 添加新渠道 - 增强版本，支持从完整渠道列表选择
     * @ENHANCEMENT 从简单输入改为渠道选择对话框，支持搜索和过滤
     */
    addNewChannel() {
        // 创建渠道选择对话框
        this.createChannelSelectionModal();
    }

    /**
     * 创建渠道选择对话框
     * @NEW_METHOD 新增方法，提供完整渠道列表选择界面
     */
    createChannelSelectionModal() {
        // 获取完整渠道列表
        const allChannels = this.getAllAvailableChannels();

        // 过滤掉已配置的渠道
        const availableChannels = allChannels.filter(channel => {
            const channelKey = this.generateChannelKey(channel);
            return !this.currentRules[channelKey];
        });

        if (availableChannels.length === 0) {
            alert('所有渠道都已配置完成！');
            return;
        }

        // 创建模态对话框
        const modal = document.createElement('div');
        modal.id = 'channel-selection-modal';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.6); display: flex; justify-content: center;
            align-items: center; z-index: 2000;
        `;

        const container = document.createElement('div');
        container.style.cssText = `
            background: #fff; padding: 25px; border-radius: 10px;
            width: 90%; max-width: 600px; max-height: 80vh; overflow: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;

        container.innerHTML = this.getChannelSelectionHTML(availableChannels);
        modal.appendChild(container);
        document.body.appendChild(modal);

        // 绑定事件
        this.bindChannelSelectionEvents(modal, availableChannels);

        // 点击外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeChannelSelectionModal();
        });
    }

    /**
     * 生成渠道选择对话框HTML
     * @NEW_METHOD 生成渠道选择界面的HTML结构
     */
    getChannelSelectionHTML(availableChannels) {
        return `
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">
                    🎯 选择要添加的渠道
                </h3>

                <!-- 搜索框 -->
                <div style="margin-bottom: 20px;">
                    <input type="text" id="channel-search" placeholder="🔍 搜索渠道名称..."
                           style="width: 100%; padding: 10px; border: 1px solid #ced4da;
                                  border-radius: 6px; font-size: 14px;">
                </div>

                <!-- 渠道列表 -->
                <div id="channel-list" style="max-height: 400px; overflow-y: auto;
                                             border: 1px solid #e9ecef; border-radius: 6px;
                                             background: #f8f9fa;">
                    ${this.generateChannelListHTML(availableChannels)}
                </div>

                <!-- 操作按钮 -->
                <div style="margin-top: 20px; text-align: center; display: flex; gap: 10px; justify-content: center;">
                    <button id="confirm-channel-btn" disabled
                            style="padding: 10px 20px; background: #28a745; color: white;
                                   border: none; border-radius: 6px; cursor: pointer;">
                        ✅ 确认添加
                    </button>
                    <button id="cancel-channel-btn"
                            style="padding: 10px 20px; background: #6c757d; color: white;
                                   border: none; border-radius: 6px; cursor: pointer;">
                        ❌ 取消
                    </button>
                </div>

                <div style="margin-top: 15px; font-size: 12px; color: #6c757d; text-align: center;">
                    共 ${availableChannels.length} 个可用渠道 | 点击渠道名称进行选择
                </div>
            </div>
        `;
    }

    /**
     * 生成渠道列表HTML
     * @NEW_METHOD 生成可选择的渠道列表
     */
    generateChannelListHTML(channels) {
        return channels.map(channel => `
            <div class="channel-option" data-channel="${channel}"
                 style="padding: 12px 15px; border-bottom: 1px solid #e9ecef;
                        cursor: pointer; transition: background-color 0.2s;
                        display: flex; align-items: center; gap: 10px;">
                <span style="font-weight: 500; color: #495057;">${channel}</span>
                <span style="font-size: 11px; color: #6c757d; margin-left: auto;">
                    ${this.getChannelCategory(channel)}
                </span>
            </div>
        `).join('');
    }

    /**
     * 绑定渠道选择对话框事件
     * @NEW_METHOD 处理渠道选择对话框的所有交互事件
     */
    bindChannelSelectionEvents(modal, availableChannels) {
        const searchInput = modal.querySelector('#channel-search');
        const channelList = modal.querySelector('#channel-list');
        const confirmBtn = modal.querySelector('#confirm-channel-btn');
        const cancelBtn = modal.querySelector('#cancel-channel-btn');

        let selectedChannel = null;

        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const filteredChannels = availableChannels.filter(channel =>
                channel.toLowerCase().includes(searchTerm)
            );
            channelList.innerHTML = this.generateChannelListHTML(filteredChannels);
            this.bindChannelItemEvents(channelList, confirmBtn, (channel) => {
                selectedChannel = channel;
            });
        });

        // 渠道选择事件
        this.bindChannelItemEvents(channelList, confirmBtn, (channel) => {
            selectedChannel = channel;
        });

        // 确认按钮
        confirmBtn.addEventListener('click', () => {
            if (selectedChannel) {
                this.addSelectedChannel(selectedChannel);
                this.closeChannelSelectionModal();
            }
        });

        // 取消按钮
        cancelBtn.addEventListener('click', () => {
            this.closeChannelSelectionModal();
        });
    }

    /**
     * 绑定渠道项目点击事件
     * @NEW_METHOD 处理单个渠道项目的选择事件
     */
    bindChannelItemEvents(container, confirmBtn, onSelect) {
        const channelOptions = container.querySelectorAll('.channel-option');

        channelOptions.forEach(option => {
            // 鼠标悬停效果
            option.addEventListener('mouseenter', () => {
                option.style.backgroundColor = '#e9ecef';
            });

            option.addEventListener('mouseleave', () => {
                if (!option.classList.contains('selected')) {
                    option.style.backgroundColor = 'transparent';
                }
            });

            // 点击选择
            option.addEventListener('click', () => {
                // 清除其他选择
                channelOptions.forEach(opt => {
                    opt.classList.remove('selected');
                    opt.style.backgroundColor = 'transparent';
                    opt.style.border = 'none';
                });

                // 标记当前选择
                option.classList.add('selected');
                option.style.backgroundColor = '#d4edda';
                option.style.border = '2px solid #28a745';

                // 启用确认按钮
                confirmBtn.disabled = false;
                confirmBtn.style.opacity = '1';
                confirmBtn.style.cursor = 'pointer';

                // 回调选择的渠道
                const channelName = option.dataset.channel;
                onSelect(channelName);
            });
        });
    }

    /**
     * 添加选中的渠道到规则
     * @NEW_METHOD 将用户选择的渠道添加到检测规则中
     */
    addSelectedChannel(channelName) {
        const channelKey = this.generateChannelKey(channelName);

        // 检查是否已存在（双重保险）
        if (this.currentRules[channelKey]) {
            alert('该渠道已存在！');
            return;
        }

        // 创建新的渠道规则
        this.currentRules[channelKey] = {
            patterns: [new RegExp('')], // 空的正则表达式，用户需要后续配置
            confidence: 0.8, // 默认置信度
            channel: channelName // 渠道显示名称
        };

        console.log(`✅ 已添加新渠道: ${channelName} (${channelKey})`);

        // 重新渲染编辑器
        this.refreshEditor();

        // 发布渠道更新事件
        this.publishChannelsUpdated();

        // 显示成功提示
        alert(`✅ 成功添加渠道: ${channelName}\n\n请为该渠道配置检测模式。`);
    }

    /**
     * 关闭渠道选择对话框
     * @NEW_METHOD 清理和关闭渠道选择模态对话框
     */
    closeChannelSelectionModal() {
        const modal = document.getElementById('channel-selection-modal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 生成渠道键名
     * @NEW_METHOD 将渠道名称转换为规则键名的标准化方法
     */
    generateChannelKey(channelName) {
        return channelName.toLowerCase()
                         .replace(/[^a-z0-9\u4e00-\u9fff]/g, '_') // 支持中文字符
                         .replace(/_+/g, '_') // 合并多个下划线
                         .replace(/^_|_$/g, ''); // 移除首尾下划线
    }

    /**
     * 获取所有可用渠道
     * @NEW_METHOD 从配置管理器获取完整的渠道列表
     */
    getAllAvailableChannels() {
        // 优先从配置管理器获取
        if (window.configManager && window.configManager.getAllChannels) {
            return window.configManager.getAllChannels();
        }

        // 回退到直接访问配置
        if (window.OTA && window.OTA.config && window.OTA.config.COMPLETE_CHANNEL_LIST) {
            return window.OTA.config.COMPLETE_CHANNEL_LIST;
        }

        // 最后的回退选项
        console.warn('⚠️ 无法获取完整渠道列表，使用默认列表');
        return [
            'Klook West Malaysia', 'Klook Singapore', 'Kkday', 'Fliggy',
            'Traveloka', 'Ctrip West Malaysia', 'GMH Sabah', 'SMW Eric'
        ];
    }

    /**
     * 获取渠道分类标签
     * @NEW_METHOD 为渠道添加分类标签，便于用户识别
     */
    getChannelCategory(channelName) {
        const name = channelName.toLowerCase();

        if (name.includes('klook') || name.includes('kkday') || name.includes('traveloka')) {
            return 'OTA平台';
        } else if (name.includes('ctrip') || name.includes('携程') || name.includes('fliggy')) {
            return '中国OTA';
        } else if (name.includes('smw') || name.includes('gmh')) {
            return '内部渠道';
        } else if (name.includes('hotel') || name.includes('maplehome')) {
            return '酒店合作';
        } else if (name.includes('coach') || name.includes('jr')) {
            return '包车服务';
        } else {
            return '其他渠道';
        }
    }

    /**
     * 添加参考号模式
     */
    addReferencePattern() {
        const prefix = prompt('请输入参考号前缀 (2-3个大写字母):');
        if (!prefix || !/^[A-Z]{2,3}$/.test(prefix)) {
            alert('请输入2-3个大写字母作为前缀!');
            return;
        }

        if (this.currentRules.referencePatterns[prefix]) {
            alert('该前缀已存在!');
            return;
        }

        this.currentRules.referencePatterns[prefix] = {
            channel: '',
            confidence: 0.9
        };

        this.refreshEditor();
    }

    /**
     * 删除渠道
     */
    deleteChannel(channelKey) {
        if (confirm(`确定要删除渠道 "${this.currentRules[channelKey]?.channel}" 吗?`)) {
            delete this.currentRules[channelKey];
            this.refreshEditor();
        }
    }

    /**
     * 刷新编辑器
     */
    refreshEditor() {
        const container = document.getElementById('channel-rules-container');
        if (container) {
            container.innerHTML = this.renderChannelRules();
            this.bindEditorEvents();
        }
    }

    /**
     * 保存规则
     */
    async saveRules() {
        try {
            // 更新检测器规则
            if (window.channelDetector) {
                window.channelDetector.updateDetectionRules(this.currentRules);
            }
            
            // 保存到本地存储
            this.saveRulesToLocalStorage();
            
            // 发布渠道更新事件
            this.publishChannelsUpdated();
            
            // 短暂延迟后关闭编辑器，让用户看到保存成功提示
            setTimeout(() => {
                this.closeEditor();
            }, 1500);
            
            return true;
        } catch (error) {
            console.error('❌ 保存规则失败:', error);
            this.showSaveStatus('error', '保存失败: ' + error.message);
            return false;
        }
    }

    /**
     * 保存规则到API
     */
    async saveRulesToAPI() {
        // 首先保存到localStorage（快速响应）
        this.saveRulesToLocalStorage();
        
        // 检查API是否可用
        const isAPIAvailable = window.apiStatusChecker?.isAPIAvailable;
        if (!isAPIAvailable) {
            console.log('API不可用，仅保存到本地存储');
            return;
        }
        
        try {
            const apiBaseUrl = 'http://localhost:3001';
            
            // 获取当前所有渠道
            const channelsResponse = await fetch(`${apiBaseUrl}/api/channels`);
            const existingChannels = await channelsResponse.json();
            
            // 获取当前所有规则
            const rulesResponse = await fetch(`${apiBaseUrl}/api/rules`);
            const existingRules = await rulesResponse.json();
            
            // 同步渠道
            for (const [channelKey, rule] of Object.entries(this.currentRules)) {
                if (channelKey === 'referencePatterns') continue;
                
                // 查找或创建渠道
                let channel = existingChannels.find(c => c.name === channelKey);
                if (!channel) {
                    const channelResponse = await fetch(`${apiBaseUrl}/api/channels`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            name: channelKey,
                            displayName: rule.channel,
                            isActive: true,
                            description: `渠道检测规则 - ${rule.channel}`
                        })
                    });
                    channel = await channelResponse.json();
                }
                
                // 保存检测规则
                const ruleData = {
                    channelId: channel._id,
                    ruleType: 'regex',
                    pattern: JSON.stringify(rule.patterns),
                    priority: 1,
                    isActive: true,
                    description: `${rule.channel} 检测规则`
                };
                
                // 查找现有规则
                const existingRule = existingRules.find(r => r.channelId === channel._id);
                if (existingRule) {
                    await fetch(`${apiBaseUrl}/api/rules/${existingRule._id}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(ruleData)
                    });
                } else {
                    await fetch(`${apiBaseUrl}/api/rules`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(ruleData)
                    });
                }
            }
            
            console.log('规则已保存到API');
        } catch (error) {
            console.warn('API保存失败，已保存到本地存储:', error);
        }
    }

    /**
     * 保存规则到localStorage
     */
    saveRulesToLocalStorage() {
        try {
            // 使用一致的存储键
            const storageKey = 'channelDetectionRules';
            
            // 优先使用本地存储管理器
            if (this.localStorageManager && this.localStorageManager.saveData) {
                this.localStorageManager.saveData(storageKey, this.currentRules);
                console.log('✅ 规则已保存到增强存储管理器');
            } else {
                // 回退到标准localStorage
                localStorage.setItem(storageKey, JSON.stringify(this.currentRules));
                localStorage.setItem(storageKey + '_timestamp', new Date().toISOString());
                console.log('✅ 规则已保存到标准localStorage');
            }
            
            // 显示保存状态
            this.showSaveStatus('success', '规则保存成功！');
            
        } catch (error) {
            console.error('❌ 规则保存失败:', error);
            this.showSaveStatus('error', '保存失败: ' + error.message);
        }
    }

    /**
     * 显示保存状态提示
     */
    showSaveStatus(type, message) {
        // 创建状态提示
        const statusDiv = document.createElement('div');
        statusDiv.className = `save-status ${type}`;
        statusDiv.textContent = message;
        statusDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(statusDiv);
        
        // 3秒后移除
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.style.opacity = '0';
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.parentNode.removeChild(statusDiv);
                    }
                }, 300);
            }
        }, 3000);
    }

    /**
     * 发布渠道更新事件
     */
    publishChannelsUpdated() {
        const channels = Object.keys(this.currentRules || {}).filter(key => 
            !['referencePatterns', 'keywordPatterns'].includes(key)
        );
        
        const event = new CustomEvent('channelsUpdated', {
            detail: { 
                channels: channels,
                timestamp: new Date().toISOString(),
                source: 'ruleEditor'
            }
        });
        
        window.dispatchEvent(event);
        console.log('渠道更新事件已发布', channels);
    }

    /**
     * 从持久化存储加载规则
     */
    async loadRulesFromStorage() {
        try {
            const storageKey = 'channelDetectionRules';
            
            // 使用本地存储管理器
            if (this.localStorageManager && this.localStorageManager.loadData) {
                const rules = await this.localStorageManager.loadData(storageKey);
                if (rules && Object.keys(rules).length > 0) {
                    console.log('📦 从增强存储加载规则');
                    return rules;
                }
            }
            
            // 回退到标准localStorage
            const savedRules = localStorage.getItem(storageKey);
            if (savedRules) {
                console.log('📦 从标准localStorage加载规则');
                return JSON.parse(savedRules);
            }
            
            // 尝试旧的存储键（向后兼容）
            const oldRules = localStorage.getItem('channel_detection_rules');
            if (oldRules) {
                console.log('📦 从旧格式localStorage加载规则');
                const parsed = JSON.parse(oldRules);
                // 迁移到新的存储键
                localStorage.setItem(storageKey, oldRules);
                return parsed;
            }
            
            return null;
        } catch (error) {
            console.error('❌ 加载规则失败:', error);
            return null;
        }
    }

    /**
     * 将API规则格式转换为本地格式
     */
    convertAPIRulesToLocalFormat(apiRules) {
        const localRules = {};
        
        for (const rule of apiRules) {
            try {
                const patterns = JSON.parse(rule.pattern);
                localRules[rule.channelId] = {
                    patterns: patterns,
                    confidence: 0.8,
                    channel: rule.description.replace('检测规则', '').trim()
                };
            } catch (error) {
                console.warn('规则解析失败:', rule, error);
            }
        }
        
        return localRules;
    }

    /**
     * 测试规则
     */
    testRules() {
        const testText = prompt('请输入测试文本:');
        if (!testText) return;

        const result = window.channelDetector.detectChannel(testText);
        
        alert(`测试结果:\n渠道: ${result.channel || '无'}\n置信度: ${result.confidence}\n方法: ${result.method}`);
    }

    /**
     * 导出规则
     */
    exportRules() {
        const rulesJson = JSON.stringify(this.currentRules, (key, value) => {
            if (value instanceof RegExp) {
                return value.source;
            }
            return value;
        }, 2);

        const blob = new Blob([rulesJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'channel-detection-rules.json';
        a.click();
        
        URL.revokeObjectURL(url);
    }

    /**
     * 关闭编辑器
     */
    closeEditor() {
        const modal = document.getElementById('rule-editor-modal');
        if (modal) {
            modal.remove();
        }
    }
}

// 创建全局实例
window.ruleEditor = new RuleEditor();

// 全局函数
function openRuleEditor() {
    window.ruleEditor.openEditor();
}