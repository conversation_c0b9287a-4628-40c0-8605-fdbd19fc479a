/**
 * 主应用程序模块 - 模块化重构版本
 * 
 * 重构更新：
 * - 消除全局变量污染 (window.app, window.channelDetector等)
 * - 实现模块容器集成，使用依赖注入获取服务
 * - 保持UI交互和业务逻辑不变 
 * - 提供向后兼容的全局函数接口
 * 
 * 设计原则：
 * - 控制反转：通过容器获取所需服务，而非直接依赖全局变量
 * - 职责分离：专注于UI协调和事件处理
 * - 接口稳定：保持原有的用户交互方式
 * - 优雅降级：依赖不可用时自动回退到兼容模式
 * 
 * 架构说明：
 * - 作为应用程序的入口点和协调中心
 * - 负责模块容器初始化和服务获取
 * - 协调各个业务模块完成复杂的用户交互流程
 */
class ChannelDetectionEditor {
    constructor(container = null) {
        // 依赖注入 - 接收模块容器
        this.container = container;
        this.services = {};
        
        this.initializeApp();
    }
    
    /**
     * 获取服务实例（优先从容器，其次从全局）
     */
    getService(serviceName) {
        // 缓存服务实例
        if (this.services[serviceName]) {
            return this.services[serviceName];
        }
        
        let service = null;
        
        try {
            // 优先从容器获取
            if (this.container && this.container.has(serviceName)) {
                service = this.container.get(serviceName);
                console.log(`✅ 从容器获取服务: ${serviceName}`);
            }
        } catch (error) {
            console.warn(`⚠️  从容器获取服务${serviceName}失败:`, error.message);
        }
        
        // 向后兼容 - 从全局变量获取
        if (!service && typeof window !== 'undefined') {
            const globalName = this.getGlobalServiceName(serviceName);
            if (window[globalName]) {
                service = window[globalName];
                console.log(`🔄 从全局变量获取服务: ${serviceName} (${globalName})`);
            }
        }
        
        if (service) {
            this.services[serviceName] = service;
        }
        
        return service;
    }
    
    /**
     * 获取全局服务名称映射
     */
    getGlobalServiceName(serviceName) {
        const mapping = {
            'channelDetector': 'channelDetector',
            'fieldMapper': 'fieldMapper', 
            'gemini': 'geminiConfig',
            'config': 'configManager'
        };
        return mapping[serviceName] || serviceName;
    }

    initializeApp() {
        console.log('渠道检测编辑器应用已初始化');
        
        // 绑定事件
        this.bindEvents();
        
        // 初始演示
        // this.demoInitialData();
    }

    bindEvents() {
        // 输入框实时检测（50ms延迟）
        const inputTextarea = document.getElementById('inputContent');
        inputTextarea.addEventListener('input', (e) => {
            this.debounce(() => {
                this.autoDetectFeatures(e.target.value);
            }, 50)();
        });

        // 回车键处理
        inputTextarea.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                this.processInput();
            }
        });
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 处理输入内容（异步版本，支持Gemini API）
     */
    async processInput() {
        const inputText = document.getElementById('inputContent').value.trim();
        
        if (!inputText) {
            this.showResult('请输入内容后再处理', 'warning');
            return;
        }

        this.showLoading();

        try {
            // 1. 渠道检测（同步）
            const channelDetector = this.getService('channelDetector');
            if (!channelDetector) {
                throw new Error('渠道检测服务不可用');
            }
            
            const channelResult = channelDetector.detectChannel(inputText);
            this.displayChannelResult(channelResult);
            console.log('✅ 渠道检测完成:', channelResult);

            // 2. 字段提取和映射（异步，包含Gemini增强）
            console.log('🔄 开始字段提取和映射...');
            const fieldMapper = this.getService('fieldMapper');
            if (!fieldMapper) {
                throw new Error('字段映射服务不可用');
            }
            
            const processingResult = await fieldMapper.processCompleteData(inputText);
            this.displayFieldMapping(processingResult);
            console.log('✅ 字段处理完成:', processingResult);

            // 3. 显示完整结果
            this.showCompleteResult(inputText, channelResult, processingResult);

        } catch (error) {
            console.error('❌ 处理失败:', error);
            this.showResult(`处理失败: ${error.message}`, 'error');
        }
    }

    /**
     * 自动检测特征（实时，完整分析）
     */
    async autoDetectFeatures(text) {
        if (!text || text.length < 10) return;

        try {
            // 防止重复触发
            if (this.autoAnalyzing) return;
            this.autoAnalyzing = true;

            console.log('🔄 自动分析开始...');

            // 1. 渠道检测（快速）
            const channelDetector = this.getService('channelDetector');
            if (!channelDetector) {
                console.warn('⚠️  渠道检测服务不可用，跳过自动检测');
                return;
            }
            
            const channelResult = channelDetector.detectChannel(text);
            if (channelResult.confidence > 0.3) {
                this.displayChannelResult(channelResult, true);
            }

            // 2. 如果内容足够长，执行完整分析
            if (text.length > 50) {
                // 显示自动分析状态
                this.showAutoAnalysisStatus();
                
                // 字段提取和映射（包含Gemini）
                const fieldMapper = this.getService('fieldMapper');
                if (fieldMapper) {
                    const processingResult = await fieldMapper.processCompleteData(text);
                    this.displayFieldMapping(processingResult);
                    
                    // 显示完整结果
                    this.showCompleteResult(text, channelResult, processingResult);
                    
                    console.log('✅ 自动分析完成');
                } else {
                    console.warn('⚠️  字段映射服务不可用，跳过自动分析');
                }
            }

        } catch (error) {
            console.warn('⚠️ 自动检测失败:', error);
            // 静默处理自动检测错误，不影响用户体验
        } finally {
            this.autoAnalyzing = false;
        }
    }

    /**
     * 显示自动分析状态
     */
    showAutoAnalysisStatus() {
        const container = document.getElementById('resultContainer');
        container.innerHTML = `
            <div class="result-item">
                <strong>🔄 自动分析中...</strong>
                <p>检测到输入变化，正在智能分析内容</p>
                <div style="margin-top: 5px; font-size: 12px; color: #666;">
                    🤖 使用AI增强分析
                </div>
            </div>
        `;
    }

    /**
     * 显示渠道检测结果
     */
    displayChannelResult(result, isAuto = false) {
        const container = document.getElementById('channelResult');
        
        if (!result.channel) {
            container.innerHTML = `
                <h4>${isAuto ? '实时检测' : '渠道检测'}</h4>
                <p>未检测到特定渠道</p>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: 0%"></div>
                </div>
                <div class="detection-details">
                    方法: ${result.method || '无匹配'}
                </div>
            `;
            return;
        }

        const confidencePercent = Math.round(result.confidence * 100);
        
        container.innerHTML = `
            <h4>${isAuto ? '实时检测到: ' : '检测到: '}${result.channel}</h4>
            <div class="confidence-bar">
                <div class="confidence-fill" style="width: ${confidencePercent}%"></div>
            </div>
            <div class="detection-details">
                <strong>置信度:</strong> ${confidencePercent}%<br>
                <strong>检测方法:</strong> ${result.method}<br>
                ${result.matchedPattern ? `<strong>匹配模式:</strong> ${result.matchedPattern}<br>` : ''}
                ${result.matchedReference ? `<strong>参考号:</strong> ${result.matchedReference}<br>` : ''}
                ${result.matchedKeyword ? `<strong>关键词:</strong> ${result.matchedKeyword}<br>` : ''}
            </div>
        `;
    }

    /**
     * 显示字段映射 - 增强版本，支持地址翻译结果显示
     * @ENHANCEMENT 集成地址翻译结果的可视化显示
     */
    displayFieldMapping(processingResult) {
        // 添加防御性检查
        if (!processingResult || typeof processingResult !== 'object') {
            console.error('processingResult 对象无效:', processingResult);
            return;
        }
        
        const container = document.getElementById('fieldMapping');
        if (!container) {
            console.error('fieldMapping 容器不存在');
            return;
        }
        
        const data = processingResult.data || {};
        const translationResults = processingResult.translationResults || {};

        // 清空现有内容
        container.innerHTML = '';

        // 显示主要字段
        const mainFields = ['customer_name', 'customer_contact', 'customer_email', 'ota_reference_number', 'flight_info', 'pickup', 'destination',
                           'passenger_number', 'luggage_number', 'ota_price', 'sub_category_id', 'extra_requirement'];

        // 同时更新静态字段卡片的值
        this.updateStaticFieldValues(data);

        mainFields.forEach(field => {
            if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
                const fieldMapper = this.getService('fieldMapper');
                const fieldInfo = (fieldMapper && fieldMapper.getApiFieldDefinitions) 
                    ? fieldMapper.getApiFieldDefinitions().descriptions[field] || field
                    : field;

                const fieldCard = document.createElement('div');
                fieldCard.className = 'field-card';

                // 检查是否有翻译结果
                const hasTranslation = translationResults[field];
                const isAddressField = field === 'pickup' || field === 'destination';

                if (isAddressField && hasTranslation) {
                    // 地址字段有翻译结果
                    fieldCard.innerHTML = this.generateAddressFieldHTML(field, fieldInfo, data, translationResults[field]);
                } else {
                    // 普通字段显示
                    fieldCard.innerHTML = `
                        <h4>${field}</h4>
                        <p>${fieldInfo}</p>
                        <div class="field-value">${this.formatFieldValue(data[field])}</div>
                    `;
                }

                container.appendChild(fieldCard);
            }
        });

        // 显示翻译统计信息
        if (processingResult.addressTranslationUsed) {
            this.displayTranslationSummary(container, processingResult.translationResults);
        }

        // 验证状态
        if (processingResult.validation && !processingResult.validation.isValid) {
            const validationCard = document.createElement('div');
            validationCard.className = 'field-card';
            validationCard.style.borderLeft = '4px solid #dc3545';
            validationCard.innerHTML = `
                <h4>⚠️ 验证警告</h4>
                <p>${processingResult.validation.message || '验证失败'}</p>
                <div class="field-value" style="color: #dc3545;">
                    缺少: ${(processingResult.validation.missingFields || []).join(', ')}
                </div>
            `;
            container.appendChild(validationCard);
        }
    }

    /**
     * 更新静态字段卡片的值
     * @NEW_METHOD 更新页面上静态字段卡片显示的值
     */
    updateStaticFieldValues(data) {
        const mainFields = ['customer_name', 'customer_contact', 'customer_email', 'ota_reference_number', 'flight_info', 'pickup', 'destination',
                           'passenger_number', 'luggage_number', 'ota_price', 'sub_category_id', 'extra_requirement'];

        mainFields.forEach(field => {
            const valueElement = document.getElementById(`${field}_value`);
            if (valueElement) {
                const value = data[field];
                if (value !== undefined && value !== null && value !== '') {
                    valueElement.textContent = this.formatFieldValue(value);
                    valueElement.style.backgroundColor = '#e8f5e8';
                    valueElement.style.borderColor = '#28a745';
                    valueElement.style.color = '#155724';
                } else {
                    valueElement.textContent = '-';
                    valueElement.style.backgroundColor = '#f8f9fa';
                    valueElement.style.borderColor = '#e9ecef';
                    valueElement.style.color = '#495057';
                }
            }
        });
    }

    /**
     * 生成地址字段的HTML（包含翻译信息）
     * @NEW_METHOD 为地址字段生成包含翻译结果的HTML
     */
    generateAddressFieldHTML(field, fieldInfo, data, translationResult) {
        const confidence = translationResult.confidence;
        const translated = translationResult.translated;
        const original = translationResult.original;
        const status = translationResult.status;
        const metadata = translationResult.metadata || {};

        // 置信度颜色和图标
        let confidenceColor = '#6c757d';
        let confidenceIcon = '❌';
        let borderColor = '#dee2e6';

        if (confidence > 0.8) {
            confidenceColor = '#28a745';
            confidenceIcon = '✅';
            borderColor = '#28a745';
        } else if (confidence > 0.6) {
            confidenceColor = '#ffc107';
            confidenceIcon = '⚠️';
            borderColor = '#ffc107';
        }

        // 翻译来源信息
        const sourceInfo = metadata.source === 'hotel_database' ? '🏨 酒店数据库' :
                          metadata.source === 'airport_database' ? '✈️ 机场数据库' : '🔍 智能匹配';

        const matchTypeInfo = metadata.matchType === 'exact' ? '完整匹配' :
                             metadata.matchType === 'similar' ? '相似匹配' :
                             metadata.matchType === 'keyword' ? '关键词匹配' :
                             metadata.matchType === 'direct' ? '直接匹配' :
                             metadata.matchType === 'city' ? '城市匹配' : '模糊匹配';

        return `
            <h4>${field} ${confidenceIcon}</h4>
            <p>${fieldInfo}</p>
            <div class="field-value" style="border-left: 3px solid ${borderColor}; padding-left: 10px;">
                ${translated ? `
                    <div style="font-weight: bold; color: ${confidenceColor};">
                        ${this.formatFieldValue(data[field])}
                    </div>
                    ${original !== translated ? `
                        <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                            原文: ${original}
                        </div>
                    ` : ''}
                    <div style="font-size: 11px; color: ${confidenceColor}; margin-top: 5px; display: flex; align-items: center; gap: 10px;">
                        <span>置信度: ${Math.round(confidence * 100)}%</span>
                        <span>${sourceInfo}</span>
                        <span>${matchTypeInfo}</span>
                    </div>
                ` : `
                    <div>${this.formatFieldValue(data[field])}</div>
                    <div style="font-size: 11px; color: #6c757d; margin-top: 5px;">
                        ${status === 'not_airport' ? '非机场地址' :
                          status === 'filtered_out' ? '已过滤' :
                          status === 'no_match' ? '未找到匹配' : '翻译失败'}
                    </div>
                `}
            </div>
        `;
    }

    /**
     * 显示翻译统计摘要
     * @NEW_METHOD 显示整体翻译结果统计
     */
    displayTranslationSummary(container, translationResults) {
        const summaryCard = document.createElement('div');
        summaryCard.className = 'field-card';
        summaryCard.style.borderLeft = '4px solid #17a2b8';
        summaryCard.style.background = '#f8f9fa';

        let translationCount = 0;
        let successCount = 0;

        Object.values(translationResults).forEach(result => {
            translationCount++;
            if (result.confidence > 0.6) {
                successCount++;
            }
        });

        summaryCard.innerHTML = `
            <h4>🌐 地址翻译摘要</h4>
            <p>智能地址翻译处理结果</p>
            <div class="field-value">
                <div style="display: flex; gap: 20px; align-items: center;">
                    <span>处理字段: ${translationCount}</span>
                    <span style="color: #28a745;">成功翻译: ${successCount}</span>
                    <span style="color: #6c757d;">成功率: ${translationCount > 0 ? Math.round((successCount / translationCount) * 100) : 0}%</span>
                </div>
            </div>
        `;

        container.appendChild(summaryCard);
    }

    /**
     * 格式化字段值
     */
    formatFieldValue(value) {
        if (value === null || value === undefined) return '-';
        if (typeof value === 'boolean') return value ? '是' : '否';
        if (typeof value === 'object') return JSON.stringify(value);
        return value.toString();
    }

    /**
     * 显示完整结果（增强版，显示Gemini处理状态）
     */
    showCompleteResult(inputText, channelResult, processingResult) {
        const container = document.getElementById('resultContainer');
        if (!container) {
            console.error('resultContainer 不存在');
            return;
        }
        
        // 添加防御性检查
        if (!processingResult) {
            container.innerHTML = `
                <div class="result-item error">
                    <strong>❌ 处理失败</strong>
                    <p>处理结果无效</p>
                </div>
            `;
            return;
        }
        
        // 检测是否使用了Gemini增强
        const geminiUsed = processingResult.geminiUsed || false;
        const hasError = processingResult.error;
        const extractedCount = (processingResult.extractedFields || []).length;
        const enhancedCount = (processingResult.enhancedFields || []).length;
        
        container.innerHTML = `
            <div class="result-item success">
                <strong>✅ 处理成功</strong>
                <p>输入内容已成功解析并映射到表单字段</p>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    处理时间: ${new Date().toLocaleString()}<br>
                    本地提取字段: ${extractedCount}<br>
                    ${enhancedCount > 0 ? `AI增强字段: ${enhancedCount}<br>` : ''}
                    ${geminiUsed ? '🤖 使用Gemini AI增强' : '⚡ 仅使用本地规则'}
                    ${hasError ? `<br><span style="color: #dc3545;">错误: ${processingResult.error}</span>` : ''}
                </div>
            </div>

            <div class="result-item">
                <strong>📋 ${geminiUsed ? 'AI增强后的字段' : '本地提取的字段'}</strong>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px; overflow-x: auto;">
${JSON.stringify(processingResult.data, null, 2)}
                </pre>
            </div>

            <div class="result-item">
                <strong>🔍 渠道检测详情</strong>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px; overflow-x: auto;">
${JSON.stringify(channelResult, null, 2)}
                </pre>
            </div>

            ${geminiUsed ? `
            <div class="result-item" style="border-left-color: #6f42c1;">
                <strong>🤖 AI处理状态</strong>
                <p>Gemini API已成功增强字段提取，提高了数据准确性</p>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    增强处理: 智能识别和补充字段信息<br>
                    数据验证: 自动验证和格式化输出
                </div>
            </div>
            ` : `
            <div class="result-item warning">
                <strong>⚡ 本地处理模式</strong>
                <p>使用本地规则处理，如需更高精度请确保Gemini API配置正确</p>
            </div>
            `}

            <div class="result-item ${(processingResult.validation?.isValid) ? 'success' : 'warning'}">
                <strong>${(processingResult.validation?.isValid) ? '✅ 验证通过' : '⚠️ 验证警告'}</strong>
                <p>${processingResult.validation?.message || '验证状态未知'}</p>
                ${!(processingResult.validation?.isValid) ? 
                    `<div style="color: #dc3545; margin-top: 5px;">
                        缺失字段: ${(processingResult.validation?.missingFields || []).join(', ')}
                    </div>` : ''}
            </div>
        `;
    }

    /**
     * 显示加载状态（增强版，显示AI处理进度）
     */
    showLoading() {
        const container = document.getElementById('resultContainer');
        container.innerHTML = `
            <div class="result-item">
                <strong>⏳ 处理中...</strong>
                <p>正在分析输入内容并提取字段信息</p>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    🔄 步骤 1: 本地规则提取<br>
                    🤖 步骤 2: Gemini AI增强<br>
                    ✅ 步骤 3: 数据验证和格式化
                </div>
                <div style="margin-top: 10px;">
                    <div style="height: 4px; background: #e9ecef; border-radius: 2px; overflow: hidden;">
                        <div style="height: 100%; background: linear-gradient(90deg, #4facfe, #6f42c1); width: 60%; animation: loading 2s infinite;"></div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示结果消息
     */
    showResult(message, type = 'info') {
        const container = document.getElementById('resultContainer');
        container.innerHTML = `
            <div class="result-item ${type}">
                <strong>${type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'} ${message}</strong>
            </div>
        `;
    }

    /**
     * 清空输入
     */
    clearInput() {
        document.getElementById('inputContent').value = '';
        document.getElementById('resultContainer').innerHTML = `
            <div class="result-item">
                <strong>等待处理...</strong>
                <p>请输入内容并点击"处理输入"按钮</p>
            </div>
        `;
        
        document.getElementById('channelResult').innerHTML = `
            <h4>未检测</h4>
            <p>等待渠道检测...</p>
        `;

        document.getElementById('fieldMapping').innerHTML = `
            <div class="field-card">
                <h4>customer_name</h4>
                <p>客户姓名</p>
                <div class="field-value">-</div>
            </div>
            <div class="field-card">
                <h4>customer_contact</h4>
                <p>客户联系电话</p>
                <div class="field-value">-</div>
            </div>
            <div class="field-card">
                <h4>pickup</h4>
                <p>接客地点</p>
                <div class="field-value">-</div>
            </div>
            <div class="field-card">
                <h4>destination</h4>
                <p>目的地</p>
                <div class="field-value">-</div>
            </div>
        `;
    }

    /**
     * 初始演示数据 - 已弃用
     * @deprecated 不再提供演示数据
     */
    demoInitialData() {
        // 根据新设计，不再提供演示数据
        const inputContent = document.getElementById('inputContent');
        if (inputContent) {
            inputContent.value = '';
        }
    }

    /**
     * 编辑渠道检测规则（公开方法）
     */
    editDetectionRules() {
        const ruleEditor = this.getService('ruleEditor');
        if (ruleEditor && typeof ruleEditor.openEditor === 'function') {
            ruleEditor.openEditor();
        } else {
            // 向后兼容
            if (typeof window !== 'undefined' && window.ruleEditor) {
                window.ruleEditor.openEditor();
            } else {
                console.warn('⚠️ RuleEditor 未找到');
                alert('规则编辑器未正确初始化，请刷新页面重试');
            }
        }
    }

    /**
     * 编辑提示词片段（公开方法）
     */
    editPromptSnippets() {
        const promptEditor = this.getService('promptEditor');
        if (promptEditor && typeof promptEditor.openEditor === 'function') {
            promptEditor.openEditor();
        } else {
            // 向后兼容或默认行为
            console.log('提示词片段编辑功能');
            alert('提示词片段编辑功能（在实际项目中这里会打开编辑界面）');
        }
    }
}

// 模块工厂函数 - 替代全局实例创建
function createApplicationModule(container) {
    return new ChannelDetectionEditor(container);
}

// 全局应用实例引用（用于HTML函数调用）
let globalAppInstance = null;

// 获取应用实例（优先从容器，其次创建新实例）
function getAppInstance() {
    if (globalAppInstance) {
        return globalAppInstance;
    }
    
    // 尝试从容器获取
    if (typeof window !== 'undefined' && window.moduleContainer) {
        try {
            globalAppInstance = window.moduleContainer.get('app');
            return globalAppInstance;
        } catch (error) {
            console.warn('⚠️  从容器获取应用实例失败:', error.message);
        }
    }
    
    // 创建兼容实例
    globalAppInstance = new ChannelDetectionEditor();
    return globalAppInstance;
}

// 全局函数（用于HTML onclick，支持异步）
async function processInput() {
    const app = getAppInstance();
    await app.processInput();
}

function clearInput() {
    const app = getAppInstance();
    app.clearInput();
}

function editDetectionRules() {
    const app = getAppInstance();
    app.editDetectionRules();
}

function editPromptSnippets() {
    const app = getAppInstance();
    app.editPromptSnippets();
}

// HTML中调用的函数（向后兼容）
function openRuleEditor() {
    editDetectionRules();
}

function openPromptEditor() {
    editPromptSnippets();
}

function openOrderAnalysis() {
    // 占位符功能 - 未来可扩展
    alert('订单分析功能开发中...');
}

// 向后兼容支持 - 为现有代码提供过渡期
if (typeof window !== 'undefined' && !window.moduleContainer) {
    console.warn('⚠️  检测到传统模式，创建Application兼容实例');
    window.app = new ChannelDetectionEditor();
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('app', createApplicationModule, ['fieldMapper', 'channelDetector', 'gemini']);
    console.log('📦 Application已注册到模块容器');
}

// 初始化应用
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 DOM已加载，初始化应用...');
    
    try {
        // 优先使用模块容器初始化
        if (typeof window !== 'undefined' && window.moduleContainer) {
            console.log('🏗️  使用模块容器初始化...');
            
            // 初始化模块容器
            await window.moduleContainer.initialize();
            
            // 获取应用实例
            globalAppInstance = window.moduleContainer.get('app');
            window.app = globalAppInstance; // 向后兼容
            
            console.log('✅ 模块化应用初始化完成');
        } else {
            console.log('🔄 使用传统模式初始化...');
            
            // 传统模式初始化
            if (!window.app) {
                window.app = new ChannelDetectionEditor();
            }
            
            // 初始化规则编辑器（兼容模式）
            if (typeof RuleEditor !== 'undefined' && !window.ruleEditor) {
                window.ruleEditor = new RuleEditor();
                console.log('✅ 规则编辑器已初始化');
            }
            
            // 初始化提示词编辑器（兼容模式）
            if (typeof PromptEditor !== 'undefined' && !window.promptEditor) {
                window.promptEditor = new PromptEditor();
                console.log('✅ 提示词编辑器已初始化');
            }
            
            console.log('✅ 传统模式应用初始化完成');
        }
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        
        // 降级到兜底模式
        console.log('🚨 降级到兜底模式...');
        window.app = new ChannelDetectionEditor();
        console.log('✅ 兜底模式初始化完成');
    }
    
    console.log('🎉 应用启动完成');
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes loading {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(200%); }
    }
`;
document.head.appendChild(style);