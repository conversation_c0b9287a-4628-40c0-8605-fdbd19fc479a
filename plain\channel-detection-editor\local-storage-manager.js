/**
 * 本地存储管理模块 - 完整的本地持久化解决方案
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：crypto-utils.js（可选加密功能）
 * 被依赖：rule-editor.js, prompt-editor.js（数据持久化）
 * 全局变量：创建 window.storageManager 实例
 * 存储管理：渠道规则、提示词片段、历史记录、配置数据
 * 
 * === 核心功能 ===
 * - 统一的本地数据持久化管理
 * - 分类数据存储（渠道、规则、提示词、历史）
 * - 可选加密存储（依赖crypto-utils）
 * - 数据备份和恢复功能
 * - 存储空间管理和清理
 * 
 * === 集成点 ===
 * - rule-editor.js：保存和加载渠道检测规则
 * - prompt-editor.js：保存和加载提示词模板
 * - app.js：历史记录和用户偏好存储
 * - crypto-utils.js：可选的安全存储功能
 * 
 * === 使用场景 ===
 * - 渠道检测规则的持久化存储
 * - 提示词模板的编辑和保存
 * - 用户操作历史的记录和查看
 * - 系统配置的本地缓存
 * 
 * === 注意事项 ===
 * 支持加密和明文两种存储模式，自动降级
 * 包含数据结构检查和修复机制
 * 支持数据导入导出和批量操作
 * 使用异步初始化支持加密操作
 */

// 本地存储管理器 - 完整的本地持久化解决方案

class LocalStorageManager {
  constructor() {
    this.storageKey = 'channel_detection_editor_data';
    this.data = null;
    this.initializeAsync();
  }

  async initializeAsync() {
    this.data = await this.loadAllData();
    this.initialize();
  }

  initialize() {
    console.log('本地存储管理器已初始化');
    // 确保数据结构完整
    this.ensureDataStructure();
  }

  /**
   * 确保数据结构完整
   */
  ensureDataStructure() {
    if (!this.data.channels) this.data.channels = {};
    if (!this.data.rules) this.data.rules = {};
    if (!this.data.prompts) this.data.prompts = {};
    if (!this.data.history) this.data.history = [];
    
    this.saveAllData();
  }

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      // 优先尝试加密读取
      if (window.cryptoUtils) {
        const encryptedData = await window.cryptoUtils.secureGetItem(this.storageKey);
        if (encryptedData) {
          return encryptedData;
        }
      }
      
      // 降级到普通读取
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : this.createDefaultData();
      
    } catch (error) {
      console.error('加载本地数据失败:', error);
      return this.createDefaultData();
    }
  }

  /**
   * 创建默认数据
   */
  createDefaultData() {
    return {
      channels: {},
      rules: {},
      prompts: {},
      history: [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 保存所有数据
   */
  async saveAllData() {
    try {
      this.data.lastUpdated = new Date().toISOString();
      
      // 优先尝试加密保存
      if (window.cryptoUtils) {
        const success = await window.cryptoUtils.secureSetItem(this.storageKey, this.data);
        if (success) {
          return;
        }
      }
      
      // 降级到普通保存
      localStorage.setItem(this.storageKey, JSON.stringify(this.data));
      
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  /**
   * 渠道管理
   */
  getAllChannels() {
    return Object.values(this.data.channels);
  }

  getChannel(channelKey) {
    return this.data.channels[channelKey];
  }

  saveChannel(channelKey, channelData) {
    this.data.channels[channelKey] = {
      ...channelData,
      _id: channelKey,
      updatedAt: new Date().toISOString(),
      createdAt: this.data.channels[channelKey]?.createdAt || new Date().toISOString()
    };
    
    this.addHistory('channel', channelKey, 'save', channelData);
    this.saveAllData();
    return this.data.channels[channelKey];
  }

  deleteChannel(channelKey) {
    const deleted = this.data.channels[channelKey];
    if (deleted) {
      delete this.data.channels[channelKey];
      this.addHistory('channel', channelKey, 'delete', deleted);
      this.saveAllData();
    }
    return deleted;
  }

  /**
   * 规则管理
   */
  getAllRules() {
    return Object.values(this.data.rules);
  }

  /**
   * 批量保存规则
   */
  saveAllRules(rules) {
    Object.entries(rules).forEach(([ruleKey, ruleData]) => {
      this.saveRule(ruleKey, ruleData);
    });
    
    this.addHistory('rule', 'all', 'batch_save', { count: Object.keys(rules).length });
    return true;
  }

  getRule(ruleKey) {
    return this.data.rules[ruleKey];
  }

  saveRule(ruleKey, ruleData) {
    this.data.rules[ruleKey] = {
      ...ruleData,
      _id: ruleKey,
      updatedAt: new Date().toISOString(),
      createdAt: this.data.rules[ruleKey]?.createdAt || new Date().toISOString()
    };
    
    this.addHistory('rule', ruleKey, 'save', ruleData);
    this.saveAllData();
    return this.data.rules[ruleKey];
  }

  deleteRule(ruleKey) {
    const deleted = this.data.rules[ruleKey];
    if (deleted) {
      delete this.data.rules[ruleKey];
      this.addHistory('rule', ruleKey, 'delete', deleted);
      this.saveAllData();
    }
    return deleted;
  }

  /**
   * 提示词管理
   */
  getAllPrompts() {
    return this.data.prompts;
  }

  getPrompt(channel, field) {
    return this.data.prompts[channel]?.[field];
  }

  savePrompt(channel, field, content) {
    if (!this.data.prompts[channel]) {
      this.data.prompts[channel] = {};
    }
    
    this.data.prompts[channel][field] = {
      content: content,
      field: field,
      channel: channel,
      updatedAt: new Date().toISOString(),
      createdAt: this.data.prompts[channel]?.[field]?.createdAt || new Date().toISOString()
    };
    
    this.addHistory('prompt', `${channel}_${field}`, 'save', { content });
    this.saveAllData();
    return this.data.prompts[channel][field];
  }

  deletePrompt(channel, field) {
    const deleted = this.data.prompts[channel]?.[field];
    if (deleted) {
      delete this.data.prompts[channel][field];
      this.addHistory('prompt', `${channel}_${field}`, 'delete', deleted);
      this.saveAllData();
    }
    return deleted;
  }

  /**
   * 变更历史
   */
  addHistory(entityType, entityId, action, data) {
    this.data.history.unshift({
      _id: Date.now().toString(),
      entityType: entityType,
      entityId: entityId,
      action: action,
      data: data,
      timestamp: new Date().toISOString()
    });
    
    // 保持最近100条历史记录
    if (this.data.history.length > 100) {
      this.data.history = this.data.history.slice(0, 100);
    }
  }

  getHistory(entityType = null, entityId = null) {
    let history = this.data.history;
    
    if (entityType) {
      history = history.filter(item => item.entityType === entityType);
    }
    
    if (entityId) {
      history = history.filter(item => item.entityId === entityId);
    }
    
    return history.slice(0, 50); // 返回最近50条
  }

  /**
   * 导出数据
   */
  exportData() {
    return {
      ...this.data,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * 导入数据
   */
  importData(importedData) {
    if (importedData && typeof importedData === 'object') {
      this.data = {
        ...this.createDefaultData(),
        ...importedData,
        lastUpdated: new Date().toISOString()
      };
      this.saveAllData();
      this.addHistory('system', 'all', 'import', { source: 'external' });
      return true;
    }
    return false;
  }

  /**
   * 清空所有数据
   */
  clearAllData() {
    this.data = this.createDefaultData();
    this.saveAllData();
    this.addHistory('system', 'all', 'clear', {});
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      channels: Object.keys(this.data.channels).length,
      rules: Object.keys(this.data.rules).length,
      prompts: Object.keys(this.data.prompts).reduce((count, channel) => 
        count + Object.keys(this.data.prompts[channel] || {}).length, 0),
      history: this.data.history.length,
      lastUpdated: this.data.lastUpdated,
      storageSize: JSON.stringify(this.data).length
    };
  }

  /**
   * 搜索功能
   */
  searchContent(query) {
    const results = [];
    const searchText = query.toLowerCase();
    
    // 搜索渠道
    Object.entries(this.data.channels).forEach(([key, channel]) => {
      if (JSON.stringify(channel).toLowerCase().includes(searchText)) {
        results.push({ type: 'channel', id: key, data: channel });
      }
    });
    
    // 搜索规则
    Object.entries(this.data.rules).forEach(([key, rule]) => {
      if (JSON.stringify(rule).toLowerCase().includes(searchText)) {
        results.push({ type: 'rule', id: key, data: rule });
      }
    });
    
    // 搜索提示词
    Object.entries(this.data.prompts).forEach(([channel, fields]) => {
      Object.entries(fields).forEach(([field, prompt]) => {
        if (prompt.content.toLowerCase().includes(searchText)) {
          results.push({ type: 'prompt', id: `${channel}_${field}`, data: prompt });
        }
      });
    });
    
    return results;
  }
}

// 创建全局实例
window.localStorageManager = new LocalStorageManager();

// 工具函数
function exportEditorData() {
  const data = window.localStorageManager.exportData();
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'channel-editor-backup.json';
  a.click();
  URL.revokeObjectURL(url);
}

function importEditorData(file) {
  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const data = JSON.parse(e.target.result);
      if (window.localStorageManager.importData(data)) {
        alert('数据导入成功！');
        location.reload();
      } else {
        alert('数据导入失败：格式错误');
      }
    } catch (error) {
      alert('数据导入失败：' + error.message);
    }
  };
  reader.readAsText(file);
}

function clearEditorData() {
  if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
    window.localStorageManager.clearAllData();
    alert('数据已清空');
    location.reload();
  }
}

function showStorageStats() {
  const stats = window.localStorageManager.getStats();
  alert(`数据统计：
渠道: ${stats.channels} 个
规则: ${stats.rules} 条
提示词: ${stats.prompts} 个
历史记录: ${stats.history} 条
最后更新: ${new Date(stats.lastUpdated).toLocaleString()}
存储大小: ${Math.round(stats.storageSize / 1024)} KB`);
}

// 初始化完成后显示状态
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    const stats = window.localStorageManager.getStats();
    console.log('本地存储初始化完成', stats);
  }, 1000);
});