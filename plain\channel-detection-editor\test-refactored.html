<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重构验证测试 - 渠道检测编辑器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px;
            border: 1px solid #e9ecef;
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #4facfe;
        }
        .test-result {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 8px;
            margin-top: 5px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-test { background: #007bff; color: white; }
        .btn-clear { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 重构验证测试</h1>
            <p>验证模块化重构后的系统功能完整性</p>
        </div>

        <div class="test-section">
            <h2>📋 模块容器测试</h2>
            <div id="container-tests">
                <div class="test-item">
                    <strong>容器初始化测试</strong>
                    <button class="btn-test" onclick="testContainerInit()">测试</button>
                    <div class="test-result" id="container-init-result">等待测试...</div>
                </div>
                <div class="test-item">
                    <strong>依赖关系验证</strong>
                    <button class="btn-test" onclick="testDependencies()">测试</button>
                    <div class="test-result" id="dependencies-result">等待测试...</div>
                </div>
                <div class="test-item">
                    <strong>模块注册检查</strong>
                    <button class="btn-test" onclick="testModuleRegistration()">测试</button>
                    <div class="test-result" id="registration-result">等待测试...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 核心功能测试</h2>
            <div id="function-tests">
                <div class="test-item">
                    <strong>渠道检测功能</strong>
                    <button class="btn-test" onclick="testChannelDetection()">测试</button>
                    <div class="test-result" id="channel-detection-result">等待测试...</div>
                </div>
                <div class="test-item">
                    <strong>字段映射功能</strong>
                    <button class="btn-test" onclick="testFieldMapping()">测试</button>
                    <div class="test-result" id="field-mapping-result">等待测试...</div>
                </div>
                <div class="test-item">
                    <strong>Gemini服务集成</strong>
                    <button class="btn-test" onclick="testGeminiIntegration()">测试</button>
                    <div class="test-result" id="gemini-integration-result">等待测试...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔄 兼容性测试</h2>
            <div id="compatibility-tests">
                <div class="test-item">
                    <strong>全局函数兼容性</strong>
                    <button class="btn-test" onclick="testGlobalFunctions()">测试</button>
                    <div class="test-result" id="global-functions-result">等待测试...</div>
                </div>
                <div class="test-item">
                    <strong>服务降级测试</strong>
                    <button class="btn-test" onclick="testServiceDegradation()">测试</button>
                    <div class="test-result" id="service-degradation-result">等待测试...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 性能比较</h2>
            <div id="performance-tests">
                <div class="test-item">
                    <strong>内存使用对比</strong>
                    <button class="btn-test" onclick="testMemoryUsage()">测试</button>
                    <div class="test-result" id="memory-usage-result">等待测试...</div>
                </div>
                <div class="test-item">
                    <strong>初始化性能</strong>
                    <button class="btn-test" onclick="testInitPerformance()">测试</button>
                    <div class="test-result" id="init-performance-result">等待测试...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 综合测试</h2>
            <div class="test-item">
                <strong>运行所有测试</strong>
                <button class="btn-test" onclick="runAllTests()" style="font-size: 16px; padding: 12px 24px;">运行全部测试</button>
                <button class="btn-clear" onclick="clearAllResults()">清除结果</button>
                <div class="test-result" id="all-tests-result">点击运行全部测试...</div>
            </div>
        </div>
    </div>

    <!-- 加载原有的脚本 -->
    <script src="module-container.js"></script>
    <script src="error-handler.js"></script>
    <script src="crypto-utils.js"></script>
    <script src="local-storage-manager.js"></script>
    <script src="data.js"></script>
    <script src="../hotels_by_region.js"></script>
    <script src="airport-data.js"></script>
    <script src="config.js"></script>
    <script src="gemini-config.js"></script>
    <script src="prompt-segmenter.js"></script>
    <script src="prompt-composer.js"></script>
    <script src="address-translator.js"></script>
    <script src="channel-detector.js"></script>
    <script src="field-mapper.js"></script>
    <script src="rule-editor.js"></script>
    <script src="prompt-editor.js"></script>
    <script src="app.js"></script>

    <script>
        // 测试用例实现
        function updateResult(id, content, type = '') {
            const element = document.getElementById(id);
            element.innerHTML = content;
            element.parentElement.className = `test-item ${type}`;
        }

        function testContainerInit() {
            try {
                const container = window.moduleContainer;
                if (!container) {
                    updateResult('container-init-result', '❌ 模块容器未初始化', 'error');
                    return;
                }

                const methods = ['register', 'get', 'has', 'initialize'];
                const missing = methods.filter(m => typeof container[m] !== 'function');
                
                if (missing.length > 0) {
                    updateResult('container-init-result', `❌ 缺少方法: ${missing.join(', ')}`, 'error');
                    return;
                }

                updateResult('container-init-result', `✅ 容器初始化成功\n方法: ${methods.join(', ')}\n工厂数量: ${container.factories.size}`, 'success');
            } catch (error) {
                updateResult('container-init-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testDependencies() {
            try {
                const container = window.moduleContainer;
                const errors = container.validateDependencies();
                
                if (errors.length > 0) {
                    updateResult('dependencies-result', `⚠️  发现依赖问题:\n${errors.join('\n')}`, 'warning');
                } else {
                    const graph = container.getDependencyGraph();
                    updateResult('dependencies-result', `✅ 依赖关系验证通过\n依赖图:\n${JSON.stringify(graph, null, 2)}`, 'success');
                }
            } catch (error) {
                updateResult('dependencies-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testModuleRegistration() {
            try {
                const container = window.moduleContainer;
                const expectedModules = ['config', 'gemini', 'channelDetector', 'fieldMapper', 'app'];
                const registered = [];
                const missing = [];

                expectedModules.forEach(module => {
                    if (container.has(module)) {
                        registered.push(module);
                    } else {
                        missing.push(module);
                    }
                });

                let result = `已注册模块: ${registered.join(', ')}`;
                if (missing.length > 0) {
                    result += `\n⚠️  缺失模块: ${missing.join(', ')}`;
                    updateResult('registration-result', result, 'warning');
                } else {
                    result += '\n✅ 所有核心模块已注册';
                    updateResult('registration-result', result, 'success');
                }
            } catch (error) {
                updateResult('registration-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testChannelDetection() {
            try {
                const testText = '订单编号: 1234567890123456789 飞猪平台预订';
                let result = '';

                // 测试容器版本
                if (window.moduleContainer && window.moduleContainer.has('channelDetector')) {
                    const detector = window.moduleContainer.get('channelDetector');
                    const detection = detector.detectChannel(testText);
                    result += `容器版本: ${detection.channel || '未检测到'} (置信度: ${Math.round(detection.confidence * 100)}%)\n`;
                }

                // 测试全局版本
                if (window.channelDetector) {
                    const detection = window.channelDetector.detectChannel(testText);
                    result += `全局版本: ${detection.channel || '未检测到'} (置信度: ${Math.round(detection.confidence * 100)}%)`;
                }

                updateResult('channel-detection-result', result ? `✅ 渠道检测功能正常\n${result}` : '❌ 渠道检测器不可用', result ? 'success' : 'error');
            } catch (error) {
                updateResult('channel-detection-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testFieldMapping() {
            try {
                const testText = '客户: 张三 电话: +86-13800138000 从机场到酒店';
                let result = '';

                // 测试容器版本
                if (window.moduleContainer && window.moduleContainer.has('fieldMapper')) {
                    const mapper = window.moduleContainer.get('fieldMapper');
                    const mapping = await mapper.processCompleteData(testText);
                    result += `容器版本: 提取 ${Object.keys(mapping.data || {}).length} 个字段\n`;
                }

                // 测试全局版本
                if (window.fieldMapper) {
                    const mapping = await window.fieldMapper.processCompleteData(testText);
                    result += `全局版本: 提取 ${Object.keys(mapping.data || {}).length} 个字段`;
                }

                updateResult('field-mapping-result', result ? `✅ 字段映射功能正常\n${result}` : '❌ 字段映射器不可用', result ? 'success' : 'error');
            } catch (error) {
                updateResult('field-mapping-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testGeminiIntegration() {
            try {
                let result = '';

                // 测试容器版本
                if (window.moduleContainer && window.moduleContainer.has('gemini')) {
                    const gemini = window.moduleContainer.get('gemini');
                    result += `容器版本: API密钥 ${gemini.isApiKeyValid() ? '有效' : '无效'}\n`;
                }

                // 测试全局版本  
                if (window.geminiConfig) {
                    result += `全局版本: API密钥 ${window.geminiConfig.isApiKeyValid() ? '有效' : '无效'}`;
                }

                updateResult('gemini-integration-result', result ? `✅ Gemini集成正常\n${result}` : '❌ Gemini服务不可用', result ? 'success' : 'error');
            } catch (error) {
                updateResult('gemini-integration-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testGlobalFunctions() {
            try {
                const functions = ['processInput', 'clearInput', 'editDetectionRules', 'editPromptSnippets'];
                const available = functions.filter(fn => typeof window[fn] === 'function');
                const missing = functions.filter(fn => typeof window[fn] !== 'function');

                let result = `可用函数: ${available.join(', ')}`;
                if (missing.length > 0) {
                    result += `\n❌ 缺失函数: ${missing.join(', ')}`;
                    updateResult('global-functions-result', result, 'warning');
                } else {
                    result += '\n✅ 所有全局函数可用';
                    updateResult('global-functions-result', result, 'success');
                }
            } catch (error) {
                updateResult('global-functions-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testServiceDegradation() {
            try {
                // 测试应用实例的服务获取能力
                const app = window.app;
                if (!app || typeof app.getService !== 'function') {
                    updateResult('service-degradation-result', '❌ 应用实例不支持服务获取', 'error');
                    return;
                }

                const services = ['channelDetector', 'fieldMapper', 'gemini'];
                let result = '服务获取测试:\n';
                
                services.forEach(service => {
                    const instance = app.getService(service);
                    result += `${service}: ${instance ? '✅' : '❌'}\n`;
                });

                updateResult('service-degradation-result', `✅ 服务降级机制正常\n${result}`, 'success');
            } catch (error) {
                updateResult('service-degradation-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testMemoryUsage() {
            try {
                const beforeGC = window.performance.memory ? {
                    used: Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(window.performance.memory.totalJSHeapSize / 1024 / 1024)
                } : { used: 'N/A', total: 'N/A' };

                const globalVars = Object.keys(window).filter(key => 
                    key.includes('channelDetector') || 
                    key.includes('fieldMapper') || 
                    key.includes('geminiConfig') ||
                    key.includes('configManager') ||
                    key.includes('moduleContainer')
                );

                updateResult('memory-usage-result', `内存使用: ${beforeGC.used}MB / ${beforeGC.total}MB\n全局变量: ${globalVars.length} 个\n变量列表: ${globalVars.join(', ')}`, 'success');
            } catch (error) {
                updateResult('memory-usage-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function testInitPerformance() {
            const startTime = performance.now();
            
            try {
                // 模拟重新初始化
                if (window.moduleContainer) {
                    window.moduleContainer.initialize().then(() => {
                        const endTime = performance.now();
                        updateResult('init-performance-result', `✅ 初始化耗时: ${Math.round(endTime - startTime)}ms\n模块数量: ${window.moduleContainer.modules.size}`, 'success');
                    }).catch(error => {
                        updateResult('init-performance-result', `❌ 初始化失败: ${error.message}`, 'error');
                    });
                } else {
                    updateResult('init-performance-result', '❌ 模块容器不可用', 'error');
                }
            } catch (error) {
                updateResult('init-performance-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            const startTime = performance.now();
            updateResult('all-tests-result', '🔄 正在运行所有测试...', '');
            
            try {
                testContainerInit();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testDependencies();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testModuleRegistration();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testChannelDetection();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                await testFieldMapping();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testGeminiIntegration();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testGlobalFunctions();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testServiceDegradation();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testMemoryUsage();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                testInitPerformance();
                
                const endTime = performance.now();
                updateResult('all-tests-result', `✅ 所有测试完成\n总耗时: ${Math.round(endTime - startTime)}ms\n请查看各项测试结果`, 'success');
                
            } catch (error) {
                updateResult('all-tests-result', `❌ 测试执行失败: ${error.message}`, 'error');
            }
        }

        function clearAllResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                if (result.id !== 'all-tests-result') {
                    result.innerHTML = '等待测试...';
                    result.parentElement.className = 'test-item';
                }
            });
            updateResult('all-tests-result', '点击运行全部测试...', '');
        }

        // 页面加载完成后自动运行基本测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testContainerInit();
                testModuleRegistration();
            }, 1000);
        });
    </script>
</body>
</html>