/**
 * 提示词分割器模块 - 通用提示词按字段切割工具
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（独立的文本处理工具）
 * 被依赖：prompt-composer.js（片段组合）, prompt-editor.js（片段分析）
 * 全局变量：创建 PromptSegmenter 实例（非全局）
 * 文本处理：正则表达式模式匹配、字段识别、内容分割
 * 
 * === 核心功能 ===
 * - 提示词文本的智能字段分割
 * - 多语言注释标记识别（#、//、<!--、*）
 * - 字段内容提取和分类
 * - 分割结果的结构化输出
 * - 自定义字段模式配置
 * 
 * === 集成点 ===
 * - prompt-composer.js：将完整提示词分割为字段片段
 * - prompt-editor.js：分析和编辑现有的提示词片段
 * - 可作为独立工具使用，无需其他依赖
 * 
 * === 使用场景 ===
 * - 长提示词文本的智能分割和分类
 * - 多字段提示词模板的解析和处理
 * - 提示词片段的提取和重组
 * - 文本内容的模式识别和结构化
 * 
 * === 注意事项 ===
 * 通用提示词按字段切割工具
 * 支持多种注释格式和语言标记
 * 可配置的字段识别模式
 * 轻量级工具，无外部依赖
 */

// 通用提示词按字段切割工具

class PromptSegmenter {
  constructor() {
    this.fieldPatterns = {
      // 基础信息字段
      base: /^(?:#|\/\/|<!--|\*)\s*基础模板|Base Template/i,
      validation: /^(?:#|\/\/|<!--|\*)\s*验证规则|Validation Rules/i,
      schema: /^(?:#|\/\/|<!--|\*)\s*输出格式|Output Schema/i,
      
      // 渠道相关字段
      ota: /^(?:#|\/\/|<!--|\*)\s*渠道标识|OTA Identification/i,
      ota_price: /^(?:#|\/\/|<!--|\*)\s*价格处理|Price Handling/i,
      
      // 客户信息字段
      customer: /^(?:#|\/\/|<!--|\*)\s*客户信息|Customer Information/i,
      customer_name: /^(?:#|\/\/|<!--|\*)\s*客户姓名|Customer Name/i,
      customer_contact: /^(?:#|\/\/|<!--|\*)\s*联系方式|Contact Information/i,
      customer_email: /^(?:#|\/\/|<!--|\*)\s*邮箱地址|Email Address/i,
      
      // 订单信息字段
      flight: /^(?:#|\/\/|<!--|\*)\s*航班信息|Flight Information/i,
      location: /^(?:#|\/\/|<!--|\*)\s*位置信息|Location Information/i,
      pickup: /^(?:#|\/\/|<!--|\*)\s*上车地点|Pickup Location/i,
      destination: /^(?:#|\/\/|<!--|\*)\s*目的地|Destination/i,
      
      // 服务需求字段
      requirements: /^(?:#|\/\/|<!--|\*)\s*特殊需求|Special Requirements/i,
      passenger_number: /^(?:#|\/\/|<!--|\*)\s*乘客数量|Passenger Count/i,
      luggage_number: /^(?:#|\/\/|<!--|\*)\s*行李数量|Luggage Count/i,
      
      // 其他字段
      extra: /^(?:#|\/\/|<!--|\*)\s*额外说明|Additional Notes/i,
      format: /^(?:#|\/\/|<!--|\*)\s*格式要求|Format Requirements/i
    };
    
    this.defaultFieldOrder = [
      'base', 'validation', 'schema', 
      'ota', 'ota_price',
      'customer', 'customer_name', 'customer_contact', 'customer_email',
      'flight', 'location', 'pickup', 'destination',
      'requirements', 'passenger_number', 'luggage_number',
      'extra', 'format'
    ];
  }

  /**
   * 将通用提示词按字段切割
   */
  segmentPrompt(fullPrompt, targetFields = null) {
    const lines = fullPrompt.split('\n');
    const segments = {};
    let currentField = null;
    let currentContent = [];
    
    for (const line of lines) {
      const fieldMatch = this.detectField(line);
      
      if (fieldMatch) {
        // 保存前一个字段的内容
        if (currentField && currentContent.length > 0) {
          segments[currentField] = currentContent.join('\n').trim();
        }
        
        // 开始新字段
        currentField = fieldMatch;
        currentContent = [line];
      } else if (currentField) {
        // 继续当前字段
        currentContent.push(line);
      } else {
        // 没有字段标识的内容，放到base字段
        if (!segments.base) {
          segments.base = [];
        }
        segments.base.push(line);
      }
    }
    
    // 保存最后一个字段
    if (currentField && currentContent.length > 0) {
      segments[currentField] = currentContent.join('\n').trim();
    }
    
    // 处理base字段
    if (segments.base && Array.isArray(segments.base)) {
      segments.base = segments.base.join('\n').trim();
    }
    
    // 过滤字段（如果指定了目标字段）
    if (targetFields) {
      const filteredSegments = {};
      for (const field of targetFields) {
        if (segments[field]) {
          filteredSegments[field] = segments[field];
        }
      }
      return filteredSegments;
    }
    
    return segments;
  }

  /**
   * 检测字段标识
   */
  detectField(line) {
    for (const [field, pattern] of Object.entries(this.fieldPatterns)) {
      if (pattern.test(line)) {
        return field;
      }
    }
    return null;
  }

  /**
   * 从片段重组提示词
   */
  recomposePrompt(segments, fieldOrder = null) {
    const order = fieldOrder || this.defaultFieldOrder;
    const lines = [];
    
    // 按指定顺序添加字段
    for (const field of order) {
      if (segments[field]) {
        lines.push(segments[field]);
        lines.push(''); // 空行分隔
      }
    }
    
    // 添加未排序的字段
    for (const [field, content] of Object.entries(segments)) {
      if (!order.includes(field)) {
        lines.push(content);
        lines.push('');
      }
    }
    
    return lines.join('\n').trim();
  }

  /**
   * 自动识别并提取字段结构
   */
  analyzePromptStructure(fullPrompt) {
    const segments = this.segmentPrompt(fullPrompt);
    const structure = [];
    
    for (const [field, content] of Object.entries(segments)) {
      structure.push({
        field: field,
        length: content.length,
        lineCount: content.split('\n').length,
        hasPlaceholders: this.hasPlaceholders(content),
        hasJson: this.hasJsonContent(content)
      });
    }
    
    return {
      totalFields: Object.keys(segments).length,
      totalLength: fullPrompt.length,
      structure: structure.sort((a, b) => b.length - a.length),
      segments: segments
    };
  }

  /**
   * 检查内容是否包含占位符
   */
  hasPlaceholders(content) {
    return /\{[^}]+\}/.test(content);
  }

  /**
   * 检查内容是否包含JSON
   */
  hasJsonContent(content) {
    return content.trim().startsWith('{') && content.trim().endsWith('}');
  }

  /**
   * 生成字段模板
   */
  generateFieldTemplate(field, exampleContent = '') {
    const templates = {
      base: `# 基础模板\n通用订单解析提示词，包含基本指令和格式要求。\n\n${exampleContent || '请严格按照输出格式要求返回JSON，字段齐全，即使为null也要包含。'}`,
      
      validation: `# 验证规则\n数据验证规则和格式要求。\n\n${exampleContent || '日期格式：YYYY-MM-DD，时间格式：HH:MM，phone允许+和分隔符。'}`,
      
      schema: `# 输出格式\n严格的JSON输出格式要求。\n\n${exampleContent || '请严格输出与API字段需求一致的JSON，仅输出纯JSON，不要包含任何解释性文字。'}`,
      
      ota: `# 渠道标识\n特定渠道的识别和处理规则。\n\n${exampleContent || '渠道专属处理：\n- 渠道标识：渠道名称\n- 特殊处理规则说明'}`,
      
      ota_price: `# 价格处理\n价格字段的特殊处理规则。\n\n${exampleContent || '价格字段特殊处理：\n- 货币类型\n- 换算公式\n- 输出格式要求'}`
    };
    
    return templates[field] || `# ${field}\n${exampleContent || '字段内容描述'}`;
  }

  /**
   * 批量处理提示词文件
   */
  async processPromptFile(fileContent, options = {}) {
    const {
      targetFields = null,
      fieldOrder = null,
      includeDefaults = false
    } = options;
    
    const segments = this.segmentPrompt(fileContent, targetFields);
    
    if (includeDefaults) {
      // 为缺失的字段添加默认模板
      for (const field of this.defaultFieldOrder) {
        if (!segments[field] && field !== 'base') {
          segments[field] = this.generateFieldTemplate(field);
        }
      }
    }
    
    return {
      segments: segments,
      recomposed: this.recomposePrompt(segments, fieldOrder),
      analysis: this.analyzePromptStructure(fileContent)
    };
  }

  /**
   * 创建可视化编辑器界面
   */
  createVisualEditor(fullPrompt = '') {
    const analysis = this.analyzePromptStructure(fullPrompt);
    
    return `
      <div style="font-family: Arial, sans-serif;">
        <h2>提示词字段分析器</h2>
        
        <div style="display: grid; grid-template-columns: 300px 1fr; gap: 20px;">
          <!-- 字段列表 -->
          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            <h3>检测到的字段 (${analysis.totalFields})</h3>
            <div style="max-height: 400px; overflow-y: auto;">
              ${analysis.structure.map(item => `
                <div style="
                  padding: 8px; margin-bottom: 5px; 
                  background: white; border: 1px solid #dee2e6; 
                  border-radius: 4px; cursor: pointer;
                " onclick="selectField('${item.field}')">
                  <strong>${item.field}</strong>
                  <div style="font-size: 12px; color: #6c757d;">
                    ${item.length}字符, ${item.lineCount}行
                    ${item.hasPlaceholders ? '📍' : ''}
                    ${item.hasJson ? '📋' : ''}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <!-- 字段内容编辑 -->
          <div>
            <h3>字段内容编辑</h3>
            <textarea 
              id="field-editor" 
              style="width: 100%; height: 300px; font-family: monospace; padding: 10px;"
              placeholder="选择左侧字段进行编辑...">
            </textarea>
            
            <div style="margin-top: 15px;">
              <button onclick="saveField()" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px;">
                保存字段
              </button>
              <button onclick="addNewField()" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; margin-left: 10px;">
                添加新字段
              </button>
            </div>
          </div>
        </div>
        
        <!-- 预览 -->
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 6px;">
          <h3>重组预览</h3>
          <pre style="background: white; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto;">
            ${this.recomposePrompt(analysis.segments)}
          </pre>
        </div>
      </div>
    `;
  }
}

// 创建全局实例
window.promptSegmenter = new PromptSegmenter();

// 工具函数
function segmentPrompt() {
  const prompt = document.getElementById('prompt-input').value;
  const result = window.promptSegmenter.segmentPrompt(prompt);
  console.log('分段结果:', result);
  return result;
}

function analyzePrompt() {
  const prompt = document.getElementById('prompt-input').value;
  const analysis = window.promptSegmenter.analyzePromptStructure(prompt);
  console.log('分析结果:', analysis);
  return analysis;
}