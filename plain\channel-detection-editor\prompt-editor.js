/**
 * 提示词片段编辑器模块 - 清理版
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：local-storage-manager.js（片段存储）, config.js（渠道列表）
 * 被依赖：app.js（提示词编辑界面）, prompt-composer.js（片段组合）
 * 全局变量：创建 PromptEditor 实例（非全局）
 * 片段管理：渠道特定提示词、通用模板、API规范模板
 * 
 * === 核心功能 ===
 * - 提示词片段的分类管理（渠道特定/通用）
 * - GoMyHire API规范模板管理
 * - 片段编辑和保存功能
 * - 片段导入导出（JSON格式）
 * - 延迟加载和缓存优化
 * 
 * === 集成点 ===
 * - app.js：提供提示词编辑界面入口
 * - local-storage-manager.js：保存和加载提示词片段
 * - config.js：获取渠道列表用于片段同步
 * - prompt-composer.js：提供片段用于智能提示词组合
 * 
 * === 使用场景 ===
 * - 渠道特定提示词模板的编辑和管理
 * - API规范模板的维护和更新
 * - 提示词片段的分类存储和快速访问
 * - 多语言和格式的提示词模板管理
 * 
 * === 注意事项 ===
 * 目的：恢复一个有效的 prompt-editor.js 文件，包含 gomyhire 片段（含返回规格骨架示例）
 * 包含严格的GoMyHire API输出规范模板
 * 支持渠道特定提示词和通用模板
 * 使用延迟加载优化性能，避免立即API调用
 */
class PromptEditor {
  constructor() {
    this.promptSnippets = this.loadDefaultSnippets();

    // 字段定义 - 用于字段选择器
    this.requiredFields = ['sub_category_id', 'ota_reference_number', 'car_type_id', 'incharge_by_backend_user_id'];
    this.optionalFields = [
      'ota', 'ota_price', 'customer_name', 'customer_contact', 'customer_email',
      'flight_info', 'pickup', 'pickup_lat', 'pickup_long', 'date', 'time',
      'destination', 'destination_lat', 'destination_long', 'passenger_number',
      'luggage_number', 'driver_fee', 'driver_collect', 'tour_guide',
      'baby_chair', 'meet_and_greet', 'extra_requirement', 'driving_region_id',
      'languages_id_array'
    ];

    // 字段描述定义
    this.fieldDefinitions = {
      'sub_category_id': { description: '服务类型ID (2:接机, 3:送机, 4:包车)' },
      'ota_reference_number': { description: 'OTA平台参考编号' },
      'car_type_id': { description: '车型ID' },
      'incharge_by_backend_user_id': { description: '后台负责人ID' },
      'ota': { description: 'OTA平台名称' },
      'ota_price': { description: 'OTA平台价格' },
      'customer_name': { description: '客户姓名' },
      'customer_contact': { description: '客户联系电话' },
      'customer_email': { description: '客户邮箱' },
      'flight_info': { description: '航班信息' },
      'pickup': { description: '接客地点' },
      'pickup_lat': { description: 'pickup_lat' },
      'pickup_long': { description: 'pickup_long' },
      'date': { description: '服务日期 (YYYY-MM-DD)' },
      'time': { description: '服务时间 (HH:MM)' },
      'destination': { description: '目的地' },
      'destination_lat': { description: 'destination_lat' },
      'destination_long': { description: 'destination_long' },
      'passenger_number': { description: '乘客数量' },
      'luggage_number': { description: '行李数量' },
      'driver_fee': { description: 'driver_fee' },
      'driver_collect': { description: 'driver_collect' },
      'tour_guide': { description: 'tour_guide' },
      'baby_chair': { description: 'baby_chair' },
      'meet_and_greet': { description: 'meet_and_greet' },
      'extra_requirement': { description: '额外要求/备注信息 (最大500字符)' },
      'driving_region_id': { description: '驾驶区域ID' },
      'languages_id_array': { description: 'languages_id_array' }
    };

    // 初始化AI优化器模块
    this.aiOptimizer = new AIOptimizer(this);

    this.initializeEditor();
    // 延迟加载存储片段，避免立即API调用
    setTimeout(() => {
      this.loadAllSnippetsFromStorage();
    }, 1000);
  }

  /**
   * 数据格式迁移：将旧格式字符串转换为新格式对象
   */
  migrateDataFormat(data) {
    if (typeof data === 'string') {
      // 旧格式：直接字符串
      return {
        enabled: true,
        content: data
      };
    } else if (data && typeof data === 'object' && data.hasOwnProperty('enabled') && data.hasOwnProperty('content')) {
      // 新格式：已经是正确格式
      return data;
    } else {
      // 无效数据
      return {
        enabled: false,
        content: ''
      };
    }
  }

  /**
   * 批量迁移渠道数据
   */
  migrateChannelData(channelData) {
    if (!channelData || typeof channelData !== 'object') {
      return {};
    }

    const migratedData = {};
    for (const [field, data] of Object.entries(channelData)) {
      migratedData[field] = this.migrateDataFormat(data);
    }
    return migratedData;
  }

  /**
   * 生成字段表单HTML - 重构版本，只显示选中的字段
   * @ENHANCEMENT 根据字段选择器的选择动态生成表单，提供更紧凑的界面
   */
  generateFieldsFormHTML() {
    const channel = document.getElementById('channel-select')?.value || 'generic';
    const selectedFields = this.getSelectedFields();

    // 如果没有选中任何字段，显示提示信息
    if (selectedFields.length === 0) {
      return `
        <div style="text-align: center; padding: 40px; color: #6c757d; background: #f8f9fa; border-radius: 6px; border: 2px dashed #dee2e6;">
          <h4 style="color: #6c757d; margin-bottom: 15px;">📝 请选择要编辑的字段</h4>
          <p style="margin-bottom: 0;">使用上方的字段选择器选择您要编辑的字段，只有选中的字段才会显示编辑表单。</p>
          <p style="margin-top: 10px; font-size: 12px;">💡 提示：可以使用快捷按钮快速选择必填字段或全部字段。</p>
        </div>
      `;
    }

    let html = '';

    // 获取API字段定义
    const requiredFields = window.fieldMapper?.apiFields?.required || this.requiredFields;
    const optionalFields = window.fieldMapper?.apiFields?.optional || this.optionalFields;
    const descriptions = window.fieldMapper?.apiFields?.descriptions || {};

    // 分离选中的必填字段和可选字段
    const selectedRequiredFields = selectedFields.filter(field => requiredFields.includes(field));
    const selectedOptionalFields = selectedFields.filter(field => optionalFields.includes(field));

    // 必填字段部分（如果有选中的必填字段）
    if (selectedRequiredFields.length > 0) {
      html += `
        <div style="margin-bottom: 30px;">
          <h3 style="color: #dc3545; margin-bottom: 15px; border-bottom: 2px solid #dc3545; padding-bottom: 5px;">
            📋 必填字段 (Required Fields) - ${selectedRequiredFields.length} 个已选择
          </h3>
          <div style="display: grid; gap: 15px;">
      `;

      selectedRequiredFields.forEach(field => {
        const fieldData = this.getFieldData(channel, field);
        const description = descriptions[field] || field;
        html += this.generateFieldHTML(field, description, fieldData, true);
      });

      html += `
          </div>
        </div>
      `;
    }

    // 可选字段部分（如果有选中的可选字段）
    if (selectedOptionalFields.length > 0) {
      html += `
        <div style="margin-bottom: 30px;">
          <h3 style="color: #28a745; margin-bottom: 15px; border-bottom: 2px solid #28a745; padding-bottom: 5px;">
            ⚙️ 可选字段 (Optional Fields) - ${selectedOptionalFields.length} 个已选择
          </h3>
          <div style="display: grid; gap: 15px;">
      `;

      selectedOptionalFields.forEach(field => {
        const fieldData = this.getFieldData(channel, field);
        const description = descriptions[field] || field;
        html += this.generateFieldHTML(field, description, fieldData, false);
      });

      html += `
          </div>
        </div>
      `;
    }

    return html;
  }

  /**
   * 生成单个字段的HTML - 重构版本，移除复选框，字段选择由选择器控制
   * @ENHANCEMENT 简化字段界面，移除复选框，选中的字段默认启用
   */
  generateFieldHTML(field, description, fieldData, isRequired) {
    const content = fieldData.content;
    const requiredClass = isRequired ? 'required-field' : 'optional-field';
    const requiredIcon = isRequired ? '🔴' : '🔵';

    return `
      <div class="field-container ${requiredClass}" style="border: 1px solid ${isRequired ? '#dc3545' : '#28a745'}; border-radius: 6px; padding: 15px; background: #f8f9fa;">
        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
          <span>${requiredIcon}</span>
          <strong style="color: ${isRequired ? '#dc3545' : '#28a745'};">${field}</strong>
          <span style="font-size: 12px; color: #6c757d;">- ${description}</span>
          <div style="margin-left: auto; display: flex; align-items: center; gap: 8px;">
            <button
              onclick="window.promptEditor.applyUniversalTemplate('${field}')"
              style="padding: 4px 8px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 10px;"
              title="应用通用模板到此字段"
            >
              📋 应用模板
            </button>
            <span style="padding: 2px 8px; background: #28a745; color: white; border-radius: 12px; font-size: 10px;">
              ✓ 已选择
            </span>
          </div>
        </div>
        <textarea
          id="content-${field}"
          placeholder="请输入 ${field} 字段的提示词内容..."
          style="width: 100%; height: 80px; font-family: monospace; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; resize: vertical; background: white;"
          oninput="window.promptEditor.onFieldContentChange('${field}')"
        >${content}</textarea>
        <div style="text-align: right; font-size: 11px; color: #6c757d; margin-top: 3px;">
          字符数: <span id="char-count-${field}">${content.length}</span>
        </div>
      </div>
    `;
  }

  /**
   * 获取字段数据
   */
  getFieldData(channel, field) {
    const channelData = this.promptSnippets[channel] || {};
    const fieldData = channelData[field];

    if (!fieldData) {
      return { enabled: false, content: '' };
    }

    // 确保数据格式正确
    return this.migrateDataFormat(fieldData);
  }

  initializeEditor() {
    console.log('PromptEditor initialized');
  }

  // 加载默认的提示词片段 - 新数据结构 {enabled, content}
  loadDefaultSnippets() {
    return {
      fliggy: {
        ota: {
          enabled: true,
          content: `Fliggy渠道专属处理：\n- 渠道标识：飞猪/Fliggy\n- 价格处理：人民币价格，需要除以1.5换算为MYR`
        },
        ota_price: {
          enabled: true,
          content: `价格字段特殊处理：\n- 货币：人民币(CNY)\n- 换算公式：原价 / 1.5\n- 输出格式：保留2位小数`
        }
      },

      jingge: {
        ota: {
          enabled: true,
          content: `JingGe渠道专属处理：\n- 渠道标识：精格商铺/JingGe Shop\n- 价格处理：马来西亚林吉特(MYR)`
        }
      },

      generic: {
        base: {
          enabled: true,
          content: `通用订单解析提示词：\n请严格按照输出格式要求返回 JSON，字段齐全，即使为 null 也要包含。`
        },
        validation: {
          enabled: true,
          content: `验证规则示例：日期 YYYY-MM-DD，时间 HH:MM，phone 允许 + 和 分隔符。`
        }
      },

      // GoMyHire 专用片段：包含智能提示词和完整API规范
      gomyhire: {
        schema: {
          enabled: true,
          content: `【GoMyHire API智能提示词系统】

【系统角色】
您是专业的旅游交通订单数据提取AI助手，专门负责从用户输入文本中准确提取并构建符合GoMyHire API规范的订单数据。

【核心提取规则】

1. 【服务类型智能识别】(sub_category_id)
   - 接机服务(2): 关键词"接机"、"机场接"、"到达接送"、"arrival"
   - 送机服务(3): 关键词"送机"、"机场送"、"departure"、"出发"
   - 包车服务(4): 关键词"包车"、"charter"、"包day"、"全天"
   - 市内游(5): 关键词"市内游"、"city tour"、"一日游"、"sightseeing"

2. 【价格信息智能提取】
   - 支持货币: RM, MYR, SGD, THB, USD, CNY
   - 数字识别: "180元"、"RM 150"、"$80"、"150马币"
   - 过滤非主要费用(小费、押金等)

3. 【地址标准化翻译】
   机场地址映射:
   - 吉隆坡国际机场/KLIA → "Kuala Lumpur International Airport (KLIA)"
   - KLIA2 → "Kuala Lumpur International Airport 2 (KLIA2)"
   - 新加坡樟宜机场 → "Singapore Changi Airport"
   - 槟城国际机场 → "Penang International Airport"
   - 亚庇国际机场 → "Kota Kinabalu International Airport"
   - 曼谷素万那普 → "Suvarnabhumi International Airport (BKK)"
   - 普吉国际机场 → "Phuket International Airport"
   
   知名地标映射:
   - 双子塔/KLCC → "Petronas Twin Towers (KLCC)"
   - 武吉免登 → "Bukit Bintang"
   - 乌节路 → "Orchard Road"
   - 圣淘沙 → "Sentosa Island"

4. 【extra_requirement智能筛选】
   包含内容:
   - 司机语言要求: "中文司机"、"英文司机"
   - 车辆特需: "大车"、"豪华车"、"婴儿座椅"、"轮椅车辆"
   - 服务要求: "准时"、"提前到达"、"帮忙搬行李"
   - 特殊乘客: "老人出行"、"儿童服务"、"无障碍需求"
   
   排除内容:
   - 已提取字段信息(姓名、电话、地址、航班等)
   - 价格、人数、行李数量等数值信息
   - 日期时间等已结构化的信息

5. 【数据格式标准化】
   - 日期: 统一YYYY-MM-DD格式
   - 时间: 24小时制HH:MM格式
   - 电话: 支持国际格式+60、+65、+86等
   - 数字: 纯数字类型，不含文字单位

【字段规范要求】
必填字段: sub_category_id, ota_reference_number, car_type_id(默认5), incharge_by_backend_user_id(默认1)
可选字段: ota, ota_price, customer_name, customer_contact, customer_email, flight_info, pickup, pickup_lat, pickup_long, date, time, destination, destination_lat, destination_long, passenger_number, luggage_number, driver_fee, driver_collect, tour_guide, baby_chair, meet_and_greet, extra_requirement, driving_region_id, languages_id_array

【输出质量要求】
- 严格JSON格式，不含任何markdown标记或解释文字
- 所有字段必须包含，未知信息设为null
- 数据类型准确：数字(number)、布尔(boolean)、字符串(string)
- 信息一致性检查：服务类型与地址、航班信息逻辑匹配

【标准输出模板】
{
  "sub_category_id": 2,
  "ota_reference_number": "订单号",
  "car_type_id": 5,
  "incharge_by_backend_user_id": 1,
  "ota": null,
  "ota_price": null,
  "customer_name": null,
  "customer_contact": null,
  "customer_email": null,
  "flight_info": null,
  "pickup": null,
  "pickup_lat": null,
  "pickup_long": null,
  "date": null,
  "time": null,
  "destination": null,
  "destination_lat": null,
  "destination_long": null,
  "passenger_number": null,
  "luggage_number": null,
  "driver_fee": null,
  "driver_collect": null,
  "tour_guide": false,
  "baby_chair": false,
  "meet_and_greet": true,
  "extra_requirement": null,
  "driving_region_id": null,
  "languages_id_array": null
}

【重要提醒】
- 优先使用本地提取结果，进行智能补充和修正
- 对不确定信息宁可设为null，避免错误推测  
- extra_requirement要精炼，避免重复其他字段信息
- 输出纯JSON，确保格式完全有效`
        }
      }
    };

  // 订单号生成规则说明（当输入中没有可用订单号时）
  // 说明：如果无法从输入文本中提取到有效的 ota_reference_number，请在生成的 JSON 中填入一个由模型生成的唯一参考号。
  // 生成规则（模型需遵守）：
  // - 由 customer_name 的拼音或首字母大写缩写 + 下单日期 (YYYYMMDD) + 连字符 + 4位随机数字 组成
  // - 示例：姓名 "Zhang San" 或 "张三"，日期 "2025-08-27" → "***********-4821"
  // - 若无法确定姓名拼音，使用姓名汉字的拼音首字母缩写；若连姓名都缺失，则使用"GUEST"作为前缀
  // - 若无法确定日期，使用当前日期（调用端时间）
  // - 该生成规则仅用于在输入缺少有效订单编号的情况下创建可追溯的参考号，模型必须将生成的号放入 ota_reference_number 字段并返回，不得将生成逻辑作为额外说明输出。
  }

  // 以下为编辑器相关的辅助方法（简化实现）
  openEditor() {
    this.createEditorModal();
  }

  createEditorModal() {
    this.closeEditor();
    const modal = document.createElement('div');
    modal.id = 'prompt-editor-modal';
    modal.style.cssText = 'position: fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.6); display:flex; justify-content:center; align-items:center; z-index:1000;';

    const container = document.createElement('div');
    container.style.cssText = 'background:#fff; padding:20px; border-radius:8px; width:90%; max-width:900px; max-height:80vh; overflow:auto;';
    container.innerHTML = this.getEditorHTML();

    modal.appendChild(container);
    document.body.appendChild(modal);

    this.bindEditorEvents();
    modal.addEventListener('click', (e) => { if (e.target === modal) this.closeEditor(); });
  }

  getEditorHTML() {
    return `
      <div>
        <h2>提示词片段编辑器 - 表单模式</h2>

        <!-- 渠道选择和同步状态 -->
        <div style="display:flex; align-items: center; gap: 15px; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
          <div style="flex: 1;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">渠道选择:</label>
            <select id="channel-select" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
              ${this.getChannelOptions()}
            </select>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <span id="sync-status" style="padding: 4px 8px; border-radius: 4px; font-size: 12px; background: #d4edda; color: #155724;">✓ 已同步</span>
            <button id="refresh-channels" style="padding: 6px 12px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 刷新</button>
          </div>
        </div>

        <!-- 字段选择器 -->
        <div style="margin-bottom: 20px; padding: 15px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
          <h4 style="margin: 0 0 15px 0; color: #856404;">📋 字段选择器</h4>
          <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 300px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">选择要编辑的字段:</label>
              <select id="field-selector" multiple style="width: 100%; min-height: 120px; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                ${this.generateFieldSelectorOptions()}
              </select>
            </div>
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <button id="select-all-fields" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                ✅ 全选
              </button>
              <button id="clear-all-fields" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                ❌ 清空
              </button>
              <button id="select-required-fields" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                🔴 必填
              </button>
            </div>
          </div>
          <div style="margin-top: 10px; font-size: 12px; color: #6c757d;">
            💡 提示：按住 Ctrl/Cmd 键可多选字段，只有选中的字段才会显示编辑表单
          </div>
        </div>

        <!-- 字段表单容器 -->
        <div id="fields-form-container">
          ${this.generateFieldsFormHTML()}
        </div>

        <!-- 实时组合预览 -->
        <div style="margin-bottom: 20px; padding: 15px; background: #e8f4fd; border-radius: 6px; border-left: 4px solid #0c63e4;">
          <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
            <h4 style="margin: 0; color: #0c63e4;">🔍 实时组合预览</h4>
            <div style="display: flex; gap: 8px;">
              <button id="refresh-preview" style="padding: 4px 8px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">刷新预览</button>
              <span id="preview-status" style="padding: 4px 8px; border-radius: 4px; font-size: 11px; background: #d4edda; color: #155724;">实时更新</span>
            </div>
          </div>
          <div id="composed-preview" style="background: white; padding: 12px; border-radius: 4px; border: 1px solid #ced4da; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; white-space: pre-wrap; color: #495057;">
            正在生成预览...
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px; font-size: 11px; color: #6c757d;">
            <span>启用字段: <span id="enabled-fields-count">0</span> | 总字符数: <span id="total-char-count">0</span></span>
            <span id="preview-timestamp">未更新</span>
          </div>
        <!-- 操作按钮 -->
        <div style="margin-top: 20px; display: flex; gap: 10px; flex-wrap: wrap; justify-content: center; padding: 15px; background: #f8f9fa; border-radius: 6px;">
          <button id="save-all-btn" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
            💾 保存所有字段
          </button>
          <button id="load-all-btn" style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
            📥 加载所有字段
          </button>
          <button id="test-compose-btn" style="padding: 10px 20px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
            🧪 测试组合
          </button>
          <button id="export-btn" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            📤 导出配置
          </button>
          <button id="reset-btn" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
            🔄 重置字段
          </button>
          <button id="system-status-btn" style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
            🔍 系统状态
          </button>
          <button id="close-btn" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            ❌ 关闭
          </button>
      </div>

      <!-- 订单内容输入模块 - AI优化功能 -->
      <div style="margin-top: 25px; padding: 20px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
        <h4 style="margin: 0 0 15px 0; color: #856404; display: flex; align-items: center; gap: 8px;">
          🤖 AI字段优化助手
          <span style="font-size: 12px; background: #ffc107; color: #212529; padding: 2px 8px; border-radius: 12px;">Gemini-2.5-Pro</span>
        </h4>

        <div style="margin-bottom: 15px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #856404;">
            📋 订单内容输入：
          </label>
          <textarea
            id="order-analysis-input"
            placeholder="请粘贴订单内容进行AI分析优化...&#10;&#10;支持格式：&#10;- 飞猪、携程、KKday、Klook等OTA订单&#10;- 任何包含客户信息、时间地点的订单文本"
            style="width: 100%; height: 120px; padding: 12px; border: 2px dashed #ffc107; border-radius: 6px; font-family: monospace; font-size: 14px; resize: vertical; background: white;"
            oninput="window.promptEditor.updateOrderInputCharCount()"
          ></textarea>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px; font-size: 12px; color: #856404;">
            <span>字符数: <span id="order-input-char-count">0</span></span>
            <span>💡 提示：AI将基于当前渠道的字段片段进行优化建议</span>
          </div>
        </div>

        <div style="text-align: center;">
          <button id="ai-optimize-btn" style="padding: 12px 24px; background: linear-gradient(45deg, #ffc107, #e0a800); color: #212529; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; font-size: 16px; box-shadow: 0 2px 4px rgba(255,193,7,0.3);">
            🚀 AI生成字段建议
          </button>
          <div style="margin-top: 8px; font-size: 12px; color: #856404;">
            基于订单内容和现有字段片段，生成优化建议
          </div>
        </div>
      </div>
    `;
  }

  bindEditorEvents() {
    // 新的按钮事件绑定
    const saveAllBtn = document.getElementById('save-all-btn');
    const loadAllBtn = document.getElementById('load-all-btn');
    const testComposeBtn = document.getElementById('test-compose-btn');
    const exportBtn = document.getElementById('export-btn');
    const resetBtn = document.getElementById('reset-btn');
    const systemStatusBtn = document.getElementById('system-status-btn');
    const closeBtn = document.getElementById('close-btn');
    const refreshChannelsBtn = document.getElementById('refresh-channels');
    const refreshPreviewBtn = document.getElementById('refresh-preview');

    // 绑定按钮事件
    saveAllBtn && saveAllBtn.addEventListener('click', () => this.saveAllFields());
    loadAllBtn && loadAllBtn.addEventListener('click', () => this.loadAllFields());
    testComposeBtn && testComposeBtn.addEventListener('click', () => this.testCompose());
    exportBtn && exportBtn.addEventListener('click', () => this.exportSnippets());
    resetBtn && resetBtn.addEventListener('click', () => this.resetAllFields());
    systemStatusBtn && systemStatusBtn.addEventListener('click', () => this.showSystemStatus());
    closeBtn && closeBtn.addEventListener('click', () => this.closeEditor());
    refreshChannelsBtn && refreshChannelsBtn.addEventListener('click', () => this.refreshChannels());
    refreshPreviewBtn && refreshPreviewBtn.addEventListener('click', () => this.updateComposedPreview());

    // 字段选择器事件绑定
    const fieldSelector = document.getElementById('field-selector');
    const selectAllFieldsBtn = document.getElementById('select-all-fields');
    const clearAllFieldsBtn = document.getElementById('clear-all-fields');
    const selectRequiredFieldsBtn = document.getElementById('select-required-fields');

    // 字段选择器变化事件
    fieldSelector && fieldSelector.addEventListener('change', () => {
      this.onFieldSelectionChange();
    });

    // 字段选择器快捷按钮事件
    selectAllFieldsBtn && selectAllFieldsBtn.addEventListener('click', () => this.selectAllFields());
    clearAllFieldsBtn && clearAllFieldsBtn.addEventListener('click', () => this.clearAllFields());
    selectRequiredFieldsBtn && selectRequiredFieldsBtn.addEventListener('click', () => this.selectRequiredFields());

    // 渠道选择事件
    const channelSelect = document.getElementById('channel-select');
    channelSelect && channelSelect.addEventListener('change', (e) => {
      this.onChannelChange(e.target.value);
    });

    // 监听渠道更新事件
    window.addEventListener('channelsUpdated', (event) => {
      this.handleChannelsUpdated(event.detail);
    });

    // AI优化按钮事件
    const aiOptimizeBtn = document.getElementById('ai-optimize-btn');
    aiOptimizeBtn && aiOptimizeBtn.addEventListener('click', () => this.analyzeOrderForPromptOptimization());

    // 标签页切换事件
    const tabFieldsBtn = document.getElementById('tab-fields');
    const tabAnalysisBtn = document.getElementById('tab-analysis');

    tabFieldsBtn && tabFieldsBtn.addEventListener('click', () => this.switchTab('fields'));
    tabAnalysisBtn && tabAnalysisBtn.addEventListener('click', () => this.switchTab('analysis'));

    // 订单分析相关事件
    const analyzeOrderBtn = document.getElementById('analyze-order-btn');
    const orderAnalysisInput = document.getElementById('order-analysis-input');
    const applyOptimizationsBtn = document.getElementById('apply-optimizations-btn');
    const viewStatisticsBtn = document.getElementById('view-statistics-btn');
    const clearHistoryBtn = document.getElementById('clear-history-btn');

    analyzeOrderBtn && analyzeOrderBtn.addEventListener('click', () => this.analyzeOrder());
    orderAnalysisInput && orderAnalysisInput.addEventListener('input', () => this.updateOrderInputCharCount());
    applyOptimizationsBtn && applyOptimizationsBtn.addEventListener('click', () => this.applyOptimizations());
    viewStatisticsBtn && viewStatisticsBtn.addEventListener('click', () => this.viewStatistics());
    clearHistoryBtn && clearHistoryBtn.addEventListener('click', () => this.clearAnalysisHistory());

    // 初始化字段选择器状态
    this.initializeFieldSelector();

    // 初始化订单分析功能
    this.initializeOrderAnalysis();

    // 初始化组合预览
    this.updateComposedPreview();
  }

  /**
   * 字段启用/禁用切换
   */
  toggleFieldEnabled(field) {
    const channel = document.getElementById('channel-select').value;
    const checkbox = document.getElementById(`enable-${field}`);
    const textarea = document.getElementById(`content-${field}`);
    const container = checkbox.closest('.field-container');

    const enabled = checkbox.checked;

    // 更新数据
    if (!this.promptSnippets[channel]) {
      this.promptSnippets[channel] = {};
    }
    if (!this.promptSnippets[channel][field]) {
      this.promptSnippets[channel][field] = { enabled: false, content: '' };
    }
    this.promptSnippets[channel][field].enabled = enabled;

    // 更新UI
    textarea.disabled = !enabled;
    textarea.style.background = enabled ? 'white' : '#e9ecef';
    container.style.background = enabled ? '#f8f9fa' : '#f1f3f4';

    // 更新启用状态文本
    const statusSpan = checkbox.nextElementSibling;
    statusSpan.textContent = enabled ? '启用' : '禁用';
    statusSpan.style.color = enabled ? '#28a745' : '#6c757d';

    // 实时更新预览
    this.debounceUpdatePreview();
  }

  /**
   * 字段内容变化处理
   */
  onFieldContentChange(field) {
    const channel = document.getElementById('channel-select').value;
    const textarea = document.getElementById(`content-${field}`);
    const charCountSpan = document.getElementById(`char-count-${field}`);

    const content = textarea.value;

    // 更新数据
    if (!this.promptSnippets[channel]) {
      this.promptSnippets[channel] = {};
    }
    if (!this.promptSnippets[channel][field]) {
      this.promptSnippets[channel][field] = { enabled: true, content: '' };
    }
    this.promptSnippets[channel][field].content = content;

    // 更新字符计数
    charCountSpan.textContent = content.length;

    // 实时更新预览（防抖动）
    this.debounceUpdatePreview();
  }

  /**
   * 防抖动更新预览
   */
  debounceUpdatePreview() {
    if (this.previewUpdateTimer) {
      clearTimeout(this.previewUpdateTimer);
    }
    this.previewUpdateTimer = setTimeout(() => {
      this.updateComposedPreview();
    }, 300); // 300ms防抖动
  }

  /**
   * 保存所有字段
   */
  async saveAllFields() {
    const channel = document.getElementById('channel-select').value;
    let savedCount = 0;
    let totalCount = 0;

    try {
      const channelData = this.promptSnippets[channel] || {};

      for (const [field, fieldData] of Object.entries(channelData)) {
        if (fieldData && fieldData.enabled && fieldData.content.trim()) {
          totalCount++;
          await this.saveSnippetToStorage(channel, field, fieldData.content);
          savedCount++;
        }
      }

      // 更新同步状态
      this.updateSyncStatus('success', `已保存 ${savedCount}/${totalCount} 个字段`);

      if (savedCount > 0) {
        console.log(`✅ 已保存 ${savedCount} 个字段到存储`);
      } else {
        console.log('⚠️ 没有启用的字段需要保存');
      }

    } catch (error) {
      console.error('❌ 保存字段失败:', error);
      this.updateSyncStatus('error', '保存失败');
    }
  }

  /**
   * 加载所有字段
   */
  async loadAllFields() {
    const channel = document.getElementById('channel-select').value;
    let loadedCount = 0;

    try {
      // 获取所有API字段
      const allFields = this.getAllApiFields();

      for (const field of allFields) {
        const content = await this.loadSnippetFromStorage(channel, field);
        if (content) {
          if (!this.promptSnippets[channel]) {
            this.promptSnippets[channel] = {};
          }
          this.promptSnippets[channel][field] = {
            enabled: true,
            content: content
          };
          loadedCount++;
        }
      }

      // 刷新界面
      this.refreshFieldsForm();
      this.updateComposedPreview();
      this.updateSyncStatus('success', `已加载 ${loadedCount} 个字段`);

      console.log(`✅ 已加载 ${loadedCount} 个字段`);

    } catch (error) {
      console.error('❌ 加载字段失败:', error);
      this.updateSyncStatus('error', '加载失败');
    }
  }

  /**
   * 重置所有字段
   */
  resetAllFields() {
    if (!confirm('确定要重置当前渠道的所有字段吗？此操作不可撤销。')) {
      return;
    }

    const channel = document.getElementById('channel-select').value;

    // 清空当前渠道数据
    this.promptSnippets[channel] = {};

    // 刷新界面
    this.refreshFieldsForm();
    this.updateComposedPreview();
    this.updateSyncStatus('warning', '已重置所有字段');

    console.log(`🔄 已重置渠道 ${channel} 的所有字段`);
  }

  /**
   * 刷新字段表单
   */
  refreshFieldsForm() {
    const container = document.getElementById('fields-form-container');
    if (container) {
      container.innerHTML = this.generateFieldsFormHTML();
    }
  }

  /**
   * 更新同步状态指示器
   */
  updateSyncStatus(type, message) {
    const statusElement = document.getElementById('sync-status');
    if (!statusElement) return;

    const statusConfig = {
      success: { bg: '#d4edda', color: '#155724', icon: '✓' },
      error: { bg: '#f8d7da', color: '#721c24', icon: '✗' },
      warning: { bg: '#fff3cd', color: '#856404', icon: '⚠' },
      info: { bg: '#d1ecf1', color: '#0c5460', icon: 'ℹ' }
    };

    const config = statusConfig[type] || statusConfig.info;
    statusElement.style.background = config.bg;
    statusElement.style.color = config.color;
    statusElement.textContent = `${config.icon} ${message}`;
  }

  /**
   * 更新组合预览 - 重构版本，基于选中的字段
   * @ENHANCEMENT 根据字段选择器的选择生成预览，而不是基于启用状态
   */
  async updateComposedPreview() {
    const channel = document.getElementById('channel-select').value;
    const previewElement = document.getElementById('composed-preview');
    const enabledCountElement = document.getElementById('enabled-fields-count');
    const totalCharCountElement = document.getElementById('total-char-count');
    const timestampElement = document.getElementById('preview-timestamp');

    if (!previewElement) return;

    try {
      // 获取选中的字段
      const selectedFields = this.getSelectedFields();
      const selectedFieldsData = {};

      // 构建选中字段的数据
      selectedFields.forEach(field => {
        const fieldData = this.getFieldData(channel, field);
        if (fieldData.content.trim()) {
          selectedFieldsData[field] = fieldData;
        }
      });

      // 更新统计信息
      enabledCountElement.textContent = selectedFields.length;
      timestampElement.textContent = new Date().toLocaleTimeString();

      if (selectedFields.length === 0) {
        previewElement.textContent = '没有选择字段，无法生成预览。请使用上方的字段选择器选择要编辑的字段。';
        totalCharCountElement.textContent = '0';
        return;
      }

      // 使用prompt-composer进行组合
      let composedPrompt = '';

      if (window.promptComposer) {
        // 使用现有的prompt-composer
        const result = await window.promptComposer.composePrompt(channel, selectedFields);
        if (result.success) {
          composedPrompt = result.composedPrompt;
        } else {
          // 降级到本地组合
          composedPrompt = this.composePromptLocally(selectedFieldsData);
        }
      } else {
        // 本地组合
        composedPrompt = this.composePromptLocally(selectedFieldsData);
      }

      // 更新预览显示
      previewElement.textContent = composedPrompt;
      totalCharCountElement.textContent = composedPrompt.length;

      // 更新预览状态
      const statusElement = document.getElementById('preview-status');
      if (statusElement) {
        statusElement.style.background = '#d4edda';
        statusElement.style.color = '#155724';
        statusElement.textContent = '预览已更新';
      }

    } catch (error) {
      console.error('❌ 更新组合预览失败:', error);
      previewElement.textContent = `预览生成失败: ${error.message}`;

      const statusElement = document.getElementById('preview-status');
      if (statusElement) {
        statusElement.style.background = '#f8d7da';
        statusElement.style.color = '#721c24';
        statusElement.textContent = '预览失败';
      }
    }
  }

  /**
   * 获取启用的字段
   */
  getEnabledFields(channel) {
    const channelData = this.promptSnippets[channel] || {};
    const enabledFields = {};

    for (const [field, fieldData] of Object.entries(channelData)) {
      const migratedData = this.migrateDataFormat(fieldData);
      if (migratedData.enabled && migratedData.content.trim()) {
        enabledFields[field] = migratedData.content;
      }
    }

    return enabledFields;
  }

  /**
   * 本地提示词组合逻辑
   */
  composePromptLocally(enabledFields) {
    const sections = [];

    // 添加基础模板
    if (enabledFields.base) {
      sections.push('=== 基础模板 ===');
      sections.push(enabledFields.base);
      sections.push('');
    }

    // 添加验证规则
    if (enabledFields.validation) {
      sections.push('=== 验证规则 ===');
      sections.push(enabledFields.validation);
      sections.push('');
    }

    // 添加输出格式
    if (enabledFields.schema) {
      sections.push('=== 输出格式 ===');
      sections.push(enabledFields.schema);
      sections.push('');
    }

    // 添加渠道特定字段
    const channelFields = Object.entries(enabledFields).filter(([field]) =>
      !['base', 'validation', 'schema'].includes(field)
    );

    if (channelFields.length > 0) {
      sections.push('=== 字段特定处理 ===');
      channelFields.forEach(([field, content]) => {
        sections.push(`## ${field}`);
        sections.push(content);
        sections.push('');
      });
    }

    return sections.join('\n').trim();
  }

  /**
   * 测试组合功能 - 支持字段模块化
   */
  async testCompose() {
    const channel = document.getElementById('channel-select').value;
    const enabledFields = this.getEnabledFields(channel);
    const enabledFieldNames = Object.keys(enabledFields);

    if (enabledFieldNames.length === 0) {
      alert('⚠️ 没有启用的字段，无法进行测试');
      return;
    }

    try {
      // 测试字段模块化组合
      const testInputText = '测试订单内容：KKday订单编号123456，客户张三，电话+852-12345678，2024-01-15 10:00接机服务';

      // 使用 prompt-composer 进行字段模块化组合
      if (window.promptComposer) {
        const result = await window.promptComposer.composePrompt(channel, enabledFieldNames, testInputText);

        if (result.success) {
          // 更新预览显示
          const previewElement = document.getElementById('composed-preview');
          if (previewElement) {
            previewElement.textContent = result.composedPrompt;
          }

          // 验证组合结果
          const validation = this.validateComposedPrompt(result.composedPrompt);

          let message = `✅ 字段模块化组合测试完成\n\n`;
          message += `渠道: ${channel}\n`;
          message += `启用字段: ${enabledFieldNames.length} 个\n`;
          message += `组合方式: ${result.isModular ? '字段模块化' : '传统方式'}\n`;
          message += `总字符数: ${result.composedPrompt.length}\n`;
          message += `验证结果: ${validation.isValid ? '✅ 通过' : '⚠️ 有问题'}\n`;

          if (!validation.isValid) {
            message += `\n问题:\n${validation.issues.join('\n')}`;
          }

          // 显示字段片段信息
          if (result.segments && Object.keys(result.segments).length > 0) {
            message += `\n\n字段片段:\n`;
            Object.keys(result.segments).forEach(field => {
              message += `• ${field}\n`;
            });
          }

          alert(message);

        } else {
          throw new Error(result.error || '组合失败');
        }
      } else {
        // 回退到传统测试方式
        await this.updateComposedPreview();

        const composedPrompt = document.getElementById('composed-preview').textContent;
        const validation = this.validateComposedPrompt(composedPrompt);

        let message = `✅ 传统组合测试完成\n\n`;
        message += `启用字段: ${enabledFieldNames.length} 个\n`;
        message += `总字符数: ${composedPrompt.length}\n`;
        message += `验证结果: ${validation.isValid ? '通过' : '有问题'}\n`;

        if (!validation.isValid) {
          message += `\n问题:\n${validation.issues.join('\n')}`;
        }

        alert(message);
      }

    } catch (error) {
      console.error('测试组合失败:', error);
      alert(`❌ 测试失败: ${error.message}`);
    }
  }

  /**
   * 验证组合后的提示词
   */
  validateComposedPrompt(prompt) {
    const issues = [];

    // 检查长度
    if (prompt.length > 4000) {
      issues.push('• 提示词过长，可能影响AI性能');
    }

    if (prompt.length < 50) {
      issues.push('• 提示词过短，可能缺少必要信息');
    }

    // 检查是否包含基本结构
    if (!prompt.includes('JSON') && !prompt.includes('json')) {
      issues.push('• 缺少JSON格式要求');
    }

    return {
      isValid: issues.length === 0,
      issues: issues,
      length: prompt.length
    };
  }

  /**
   * 刷新渠道列表
   */
  refreshChannels() {
    const channelSelect = document.getElementById('channel-select');
    if (channelSelect) {
      const currentValue = channelSelect.value;
      channelSelect.innerHTML = this.getChannelOptions();

      // 尝试保持当前选择
      if (currentValue && channelSelect.querySelector(`option[value="${currentValue}"]`)) {
        channelSelect.value = currentValue;
      }

      this.updateSyncStatus('success', '渠道列表已刷新');
    }
  }

  onChannelChange(channel) {
    console.log(`🔄 渠道切换到: ${channel}`);

    // 刷新字段表单以显示新渠道的数据
    this.refreshFieldsForm();

    // 更新组合预览
    this.updateComposedPreview();

    // 更新同步状态
    this.updateSyncStatus('info', `已切换到渠道: ${channel}`);
  }

  /**
   * 处理渠道更新事件
   */
  handleChannelsUpdated(eventDetail) {
    console.log('📡 收到渠道更新事件', eventDetail);

    // 刷新渠道选择下拉菜单
    const channelSelect = document.getElementById('channel-select');
    if (channelSelect) {
      const currentValue = channelSelect.value;
      channelSelect.innerHTML = this.getChannelOptions();

      // 保持当前选择
      if (currentValue && channelSelect.querySelector(`option[value="${currentValue}"]`)) {
        channelSelect.value = currentValue;
      }
    }

    // 更新同步状态
    this.updateSyncStatus('success', `渠道已同步 (${eventDetail.channels?.length || 0}个)`);

    // 刷新字段表单
    this.refreshFieldsForm();
  }

  /**
   * 切换字段编辑模式
   */
  toggleFieldMode(mode) {
    const batchContainer = document.getElementById('batch-fields-container');
    const fieldSelect = document.getElementById('field-select');
    
    if (mode === 'batch') {
      batchContainer.style.display = 'block';
      fieldSelect.style.display = 'none';
      this.generateBatchFieldSelector();
    } else {
      batchContainer.style.display = 'none';
      fieldSelect.style.display = 'block';
    }
  }

  /**
   * 生成批量字段选择器
   */
  generateBatchFieldSelector() {
    const container = document.getElementById('batch-fields-selector');
    if (!container) return;
    
    container.innerHTML = '';
    
    const fields = this.getAllApiFields().filter(f => !['base', 'validation', 'schema'].includes(f));
    
    fields.forEach(field => {
      const label = document.createElement('label');
      label.style.display = 'flex';
      label.style.alignItems = 'center';
      label.style.gap = '5px';
      label.style.fontSize = '12px';
      
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.value = field;
      checkbox.name = 'batch-fields';
      
      const description = window.fieldMapper?.apiFields?.descriptions?.[field] || field;
      label.appendChild(checkbox);
      label.appendChild(document.createTextNode(description));
      
      container.appendChild(label);
    });
  }

  /**
   * 切换默认值预览
   */
  toggleDefaultPreview() {
    const useDefault = document.getElementById('use-default')?.checked;
    const channel = document.getElementById('channel-select')?.value;
    const field = document.getElementById('field-select')?.value;
    
    const defaultPreview = document.getElementById('default-preview');
    const defaultContent = document.getElementById('default-content');
    
    if (useDefault && channel && field && channel !== 'generic') {
      // 显示通用渠道的默认值
      const defaultSnippet = this.promptSnippets.generic?.[field] || '';
      if (defaultSnippet) {
        defaultPreview.style.display = 'block';
        defaultContent.textContent = defaultSnippet;
      } else {
        defaultPreview.style.display = 'none';
      }
    } else {
      defaultPreview.style.display = 'none';
    }
  }

  /**
   * 更新字符计数
   */
  updateCharCount(text) {
    const charCount = document.getElementById('char-count');
    if (charCount) {
      charCount.textContent = text.length;
    }
  }

  async loadSnippet() {
    const channel = document.getElementById('channel-select').value;
    const field = document.getElementById('field-select').value;
    
    // 优先从内存加载，如果没有则从存储加载
    let content = this.promptSnippets[channel]?.[field];
    if (!content) {
      content = await this.loadSnippetFromStorage(channel, field);
      if (content) {
        if (!this.promptSnippets[channel]) this.promptSnippets[channel] = {};
        this.promptSnippets[channel][field] = content;
      }
    }
    
    document.getElementById('prompt-content').value = content || '';
    this.updatePreview(content || '');
  }

  async saveSnippet() {
    const channel = document.getElementById('channel-select').value;
    const field = document.getElementById('field-select').value;
    const content = document.getElementById('prompt-content').value;

    if (!this.promptSnippets[channel]) this.promptSnippets[channel] = {};
    this.promptSnippets[channel][field] = content;
    
    // 保存到持久化存储
    await this.saveSnippetToStorage(channel, field, content);
    alert('已保存');
  }

  /**
   * 保存提示词片段到持久化存储
   */
  async saveSnippetToStorage(channel, field, content) {
    // 使用新的本地存储管理器
    if (window.localStorageManager) {
      window.localStorageManager.savePrompt(channel, field, content);
      console.log('提示词已保存到本地存储管理器');
      return;
    }
    
    // 回退到旧的localStorage方式
    this.saveSnippetToLocalStorage(channel, field, content);
  }
      
  /**
   * 保存提示词片段到localStorage
   */
  saveSnippetToLocalStorage(channel, field, content) {
    try {
      const key = `prompt_snippet_${channel}_${field}`;
      localStorage.setItem(key, content);
      localStorage.setItem(`${key}_updated`, new Date().toISOString());
    } catch (error) {
      console.error('localStorage保存失败:', error);
    }
  }

  /**
   * 从持久化存储加载提示词片段
   */
  async loadSnippetFromStorage(channel, field) {
    // 使用新的本地存储管理器
    if (window.localStorageManager) {
      const prompt = window.localStorageManager.getPrompt(channel, field);
      if (prompt && prompt.content) {
        return prompt.content;
      }
    }
    
    // 回退到旧的localStorage方式
    const key = `prompt_snippet_${channel}_${field}`;
    const localContent = localStorage.getItem(key);
    if (localContent) {
      return localContent;
    }
    
    return '';
  }

  /**
   * 初始化时从存储加载所有片段
   */
  async loadAllSnippetsFromStorage() {
    // 动态获取所有渠道（从config.js或data.js）
    const channels = this.getAllAvailableChannels();
    const fields = this.getAllApiFields();
    
    for (const channel of channels) {
      for (const field of fields) {
        const content = await this.loadSnippetFromStorage(channel, field);
        if (content) {
          if (!this.promptSnippets[channel]) this.promptSnippets[channel] = {};
          this.promptSnippets[channel][field] = content;
        }
      }
    }
  }

  testSnippet() {
    const content = document.getElementById('prompt-content').value;
    if (!content) { alert('请输入提示词内容'); return; }
    
    // 显示预览
    this.updatePreview(content);
    
    // 简单的语法检查
    const validation = this.validateSnippet(content);
    if (validation.isValid) {
      alert('✅ 提示词语法检查通过');
    } else {
      alert(`⚠️ 提示词可能存在问题:\n${validation.issues.join('\n')}`);
    }
  }

  /**
   * 预览组合提示词
   */
  async previewComposedPrompt() {
    const channel = document.getElementById('channel-select').value;
    const field = document.getElementById('field-select').value;
    
    try {
      const apiBaseUrl = 'http://localhost:3001';
      const response = await fetch(`${apiBaseUrl}/api/composed-prompt?channelId=${channel}&fields=base,${field}`);
      
      if (response.ok) {
        const result = await response.json();
        this.updatePreview(result.composedPrompt);
        alert('组合提示词预览已生成');
      } else {
        alert('组合预览失败：API不可用');
      }
    } catch (error) {
      console.warn('组合预览失败:', error);
      alert('组合预览失败：请检查API服务器');
    }
  }

  /**
   * 验证提示词语法
   */
  validateSnippet(content) {
    const issues = [];
    
    // 检查是否包含必要的占位符
    if (content.includes('{') && !content.includes('}')) {
      issues.push('• 存在未闭合的占位符 { }');
    }
    
    // 检查JSON格式（如果看起来像JSON）
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        JSON.parse(content);
      } catch (e) {
        issues.push('• JSON格式错误: ' + e.message);
      }
    }
    
    // 检查长度限制
    if (content.length > 1000) {
      issues.push('• 提示词较长，可能会影响性能');
    }
    
    return {
      isValid: issues.length === 0,
      issues: issues
    };
  }

  exportSnippets() {
    const json = JSON.stringify(this.promptSnippets, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url; a.download = 'prompt-snippets.json'; a.click();
    URL.revokeObjectURL(url);
  }

  updatePreview(content) {
    const preview = document.getElementById('preview');
    if (!preview) return;
    if (content) { preview.style.display = 'block'; preview.textContent = content; }
    else { preview.style.display = 'none'; }
  }

  closeEditor() {
    const modal = document.getElementById('prompt-editor-modal');
    modal && modal.remove();
  }

  /**
   * 获取所有可用渠道选项 - 修正版本，只显示已配置的渠道
   * @ENHANCEMENT 从规则编辑器的已配置渠道获取数据，而不是完整渠道列表
   */
  getChannelOptions() {
    let options = '<option value="generic">Generic (通用)</option>';

    // 优先从规则编辑器获取已配置的渠道
    const configuredChannels = this.getConfiguredChannels();

    if (configuredChannels.length > 0) {
      configuredChannels.forEach(channelInfo => {
        const { key, displayName } = channelInfo;
        options += `<option value="${key}">${displayName}</option>`;
      });

      console.log(`📋 提示词编辑器渠道列表已更新，共 ${configuredChannels.length} 个已配置渠道`);
    } else {
      // 回退到默认渠道（当没有配置任何渠道时）
      console.warn('⚠️ 未找到已配置的渠道，使用默认渠道列表');
      options += '<option value="fliggy">Fliggy</option>';
      options += '<option value="jingge">JingGe</option>';
      options += '<option value="gomyhire">GoMyHire</option>';
    }

    return options;
  }

  /**
   * 获取已配置的渠道列表
   * @NEW_METHOD 从规则编辑器获取已配置的渠道信息
   */
  getConfiguredChannels() {
    const configuredChannels = [];

    try {
      // 从规则编辑器获取当前规则
      let currentRules = null;

      if (window.ruleEditor && window.ruleEditor.currentRules) {
        currentRules = window.ruleEditor.currentRules;
      } else if (window.channelDetector && window.channelDetector.getDetectionRules) {
        currentRules = window.channelDetector.getDetectionRules();
      }

      if (currentRules && typeof currentRules === 'object') {
        // 遍历规则，提取渠道信息
        Object.entries(currentRules).forEach(([channelKey, ruleData]) => {
          // 跳过系统内部使用的键
          if (['referencePatterns', 'keywordPatterns'].includes(channelKey)) {
            return;
          }

          // 提取渠道显示名称
          let displayName = channelKey;
          if (ruleData && typeof ruleData === 'object' && ruleData.channel) {
            displayName = ruleData.channel;
          }

          configuredChannels.push({
            key: channelKey,
            displayName: displayName,
            confidence: ruleData?.confidence || 0.8
          });
        });

        // 按显示名称排序
        configuredChannels.sort((a, b) => a.displayName.localeCompare(b.displayName));
      }

    } catch (error) {
      console.error('❌ 获取已配置渠道失败:', error);
    }

    return configuredChannels;
  }

  /**
   * 生成字段选择器选项
   * @NEW_METHOD 为字段选择器生成选项列表，支持分组显示
   */
  generateFieldSelectorOptions() {
    let options = '';

    // 必填字段组
    options += '<optgroup label="🔴 必填字段 (Required Fields)">';
    this.requiredFields.forEach(field => {
      const fieldInfo = this.fieldDefinitions[field];
      if (fieldInfo) {
        options += `<option value="${field}">${field} - ${fieldInfo.description}</option>`;
      }
    });
    options += '</optgroup>';

    // 可选字段组
    options += '<optgroup label="🔵 可选字段 (Optional Fields)">';
    this.optionalFields.forEach(field => {
      const fieldInfo = this.fieldDefinitions[field];
      if (fieldInfo) {
        options += `<option value="${field}">${field} - ${fieldInfo.description}</option>`;
      }
    });
    options += '</optgroup>';

    return options;
  }

  /**
   * 获取当前选中的字段列表
   * @NEW_METHOD 从字段选择器获取用户选中的字段
   */
  getSelectedFields() {
    const fieldSelector = document.getElementById('field-selector');
    if (!fieldSelector) return [];

    const selectedOptions = Array.from(fieldSelector.selectedOptions);
    return selectedOptions.map(option => option.value);
  }

  /**
   * 设置字段选择器的选中状态
   * @NEW_METHOD 根据当前启用的字段设置选择器状态
   */
  setSelectedFields(fields) {
    const fieldSelector = document.getElementById('field-selector');
    if (!fieldSelector) return;

    // 清除所有选择
    Array.from(fieldSelector.options).forEach(option => {
      option.selected = false;
    });

    // 设置指定字段为选中状态
    fields.forEach(field => {
      const option = fieldSelector.querySelector(`option[value="${field}"]`);
      if (option) {
        option.selected = true;
      }
    });
  }

  /**
   * 初始化字段选择器状态
   * @NEW_METHOD 根据当前渠道的已启用字段设置选择器状态
   */
  initializeFieldSelector() {
    const channel = document.getElementById('channel-select')?.value || 'generic';
    const enabledFields = [];

    // 获取当前渠道已启用的字段
    if (this.promptSnippets[channel]) {
      Object.entries(this.promptSnippets[channel]).forEach(([field, data]) => {
        if (data.enabled) {
          enabledFields.push(field);
        }
      });
    }

    // 如果没有已启用的字段，默认选择必填字段
    if (enabledFields.length === 0) {
      this.setSelectedFields(this.requiredFields);
    } else {
      this.setSelectedFields(enabledFields);
    }
  }

  /**
   * 字段选择变化处理
   * @NEW_METHOD 当用户改变字段选择时刷新表单
   */
  onFieldSelectionChange() {
    console.log('🔄 字段选择已变化，刷新表单');
    this.refreshFieldsForm();
    this.updateComposedPreview();
  }

  /**
   * 选择所有字段
   * @NEW_METHOD 快捷选择所有可用字段
   */
  selectAllFields() {
    const allFields = [...this.requiredFields, ...this.optionalFields];
    this.setSelectedFields(allFields);
    this.onFieldSelectionChange();
  }

  /**
   * 清空所有字段选择
   * @NEW_METHOD 清空字段选择器
   */
  clearAllFields() {
    this.setSelectedFields([]);
    this.onFieldSelectionChange();
  }

  /**
   * 选择必填字段
   * @NEW_METHOD 快捷选择所有必填字段
   */
  selectRequiredFields() {
    this.setSelectedFields(this.requiredFields);
    this.onFieldSelectionChange();
  }

  /**
   * 获取所有API字段选项
   */
  getFieldOptions() {
    let options = '';
    
    // 从field-mapper.js获取字段列表
    if (window.fieldMapper && window.fieldMapper.apiFields) {
      // 必填字段
      options += '<optgroup label="必填字段">';
      window.fieldMapper.apiFields.required.forEach(field => {
        const description = window.fieldMapper.apiFields.descriptions[field] || field;
        options += `<option value="${field}">${field} - ${description}</option>`;
      });
      options += '</optgroup>';
      
      // 可选字段
      options += '<optgroup label="可选字段">';
      window.fieldMapper.apiFields.optional.forEach(field => {
        const description = window.fieldMapper.apiFields.descriptions[field] || field;
        options += `<option value="${field}">${field} - ${description}</option>`;
      });
      options += '</optgroup>';
    } else {
      // 回退到默认字段
      options += '<option value="base">基础模板 (base)</option>';
      options += '<option value="validation">验证规则 (validation)</option>';
      options += '<option value="schema">输出格式 (schema)</option>';
      options += '<option value="ota">渠道标识 (ota)</option>';
      options += '<option value="ota_price">价格处理 (ota_price)</option>';
    }
    
    return options;
  }

  /**
   * 获取所有可用渠道 - 统一从 configManager 获取
   */
  getAllAvailableChannels() {
    const channels = ['generic'];

    if (window.configManager && window.configManager.getAllChannels) {
      const allChannels = window.configManager.getAllChannels();
      allChannels.forEach(channel => {
        if (channel && typeof channel === 'string') {
          channels.push(channel.toLowerCase());
        }
      });
    }

    return [...new Set(channels)]; // 去重
  }

  /**
   * 获取所有API字段
   */
  getAllApiFields() {
    const fields = ['base', 'validation', 'schema'];
    
    if (window.fieldMapper && window.fieldMapper.apiFields) {
      fields.push(...window.fieldMapper.apiFields.required);
      fields.push(...window.fieldMapper.apiFields.optional);
    } else {
      fields.push('ota', 'ota_price');
    }
    
    return [...new Set(fields)]; // 去重
  }

  /**
   * 更新订单输入字符计数
   */
  updateOrderInputCharCount() {
    const input = document.getElementById('order-analysis-input');
    const counter = document.getElementById('order-input-char-count');

    if (input && counter) {
      counter.textContent = input.value.length;
    }
  }

  /**
   * 应用通用模板到指定字段
   * @param {string} field - 字段名
   */
  applyUniversalTemplate(field) {
    try {
      // 获取通用模板
      const fragmentManager = this.getService('promptFragmentManager');

      if (!fragmentManager) {
        alert('提示词片段管理器不可用');
        return;
      }

      const templates = fragmentManager.getUniversalFieldTemplates();
      const template = templates[field];

      if (!template) {
        alert(`字段 "${field}" 没有可用的通用模板`);
        return;
      }

      // 写入字段textarea
      const textarea = document.getElementById(`content-${field}`);
      if (textarea) {
        textarea.value = template;

        // 触发内容变化事件，更新字符计数
        this.onFieldContentChange(field);

        // 视觉反馈
        textarea.style.background = '#d4edda';
        setTimeout(() => {
          textarea.style.background = 'white';
        }, 1000);

        console.log(`✅ 已应用通用模板到字段: ${field}`);
      } else {
        console.error(`未找到字段 ${field} 的textarea元素`);
      }

    } catch (error) {
      console.error('应用通用模板失败:', error);
      alert('应用模板失败: ' + error.message);
    }
  }

  /**
   * 分析订单内容进行提示词优化 - 使用Gemini Pro模型
   * @deprecated 使用 AIOptimizer.analyzeOrder() 替代
   */
  async analyzeOrderForPromptOptimization() {
    return this.aiOptimizer.analyzeOrder();
  }

  /**
   * 统一服务获取工具
   * @deprecated 使用全局 window.getService() 替代
   * @param {string} serviceName - 服务名称
   * @returns {Object|null} 服务实例
   */
  getService(serviceName) {
    return window.getService ? window.getService(serviceName) : null;
  }

  /**
   * 获取Gemini服务实例
   * @deprecated 使用 getService('gemini') 替代
   */
  getGeminiService() {
    return this.getService('gemini');
  }

  /**
   * 收集当前渠道的所有字段片段
   * @param {string} channel - 渠道名称
   * @returns {Object} 字段名 -> 片段内容的映射
   */
  collectCurrentFieldSnippets(channel) {
    const snippets = {};
    const channelData = this.promptSnippets[channel] || {};

    // 收集当前渠道已启用且有内容的字段片段
    for (const [field, fieldData] of Object.entries(channelData)) {
      const migratedData = this.migrateDataFormat(fieldData);
      if (migratedData.enabled && migratedData.content && migratedData.content.trim()) {
        snippets[field] = migratedData.content.trim();
      }
    }

    console.log(`📋 收集到 ${channel} 渠道的字段片段:`, Object.keys(snippets));
    return snippets;
  }







  /**
   * 系统状态验证 - 检查字段模块化功能是否正常
   */
  validateSystemStatus() {
    const status = {
      fragmentManager: false,
      promptComposer: false,
      geminiService: false,
      universalTemplates: false,
      coreFields: false
    };

    try {
      // 检查 PromptFragmentManager
      const fragmentManager = this.getService('promptFragmentManager');
      if (fragmentManager) {
        status.fragmentManager = true;

        // 检查通用模板
        if (fragmentManager.getUniversalFieldTemplates) {
          const templates = fragmentManager.getUniversalFieldTemplates();
          status.universalTemplates = Object.keys(templates).length > 0;
        }

        // 检查核心字段
        if (fragmentManager.getCoreFieldList) {
          const fields = fragmentManager.getCoreFieldList();
          status.coreFields = Array.isArray(fields) && fields.length > 0;
        }
      }

      // 检查 PromptComposer
      if (window.promptComposer && window.promptComposer.composePrompt) {
        status.promptComposer = true;
      }

      // 检查 Gemini 服务
      const geminiService = this.getService('gemini');
      if (geminiService && geminiService.callGeminiAPIWithRetry) {
        status.geminiService = true;
      }

    } catch (error) {
      console.error('系统状态验证失败:', error);
    }

    return status;
  }

  /**
   * 显示系统状态报告
   */
  showSystemStatus() {
    const status = this.validateSystemStatus();

    let report = '🔍 字段模块化系统状态报告\n\n';
    report += `📦 PromptFragmentManager: ${status.fragmentManager ? '✅ 正常' : '❌ 异常'}\n`;
    report += `📋 通用字段模板: ${status.universalTemplates ? '✅ 已加载' : '❌ 未加载'}\n`;
    report += `🔧 核心字段列表: ${status.coreFields ? '✅ 已加载' : '❌ 未加载'}\n`;
    report += `🎯 PromptComposer: ${status.promptComposer ? '✅ 正常' : '❌ 异常'}\n`;
    report += `🤖 Gemini服务: ${status.geminiService ? '✅ 正常' : '❌ 异常'}\n\n`;

    const allGood = Object.values(status).every(s => s);
    report += allGood ?
      '🎉 系统状态良好，所有字段模块化功能可用！' :
      '⚠️ 部分功能异常，请检查模块加载情况';

    alert(report);
    console.log('系统状态详情:', status);
  }
}

/**
 * AI优化器内联模块 - 负责提示词优化相关功能
 * 从 PromptEditor 中抽离，保持职责分离
 */
class AIOptimizer {
  constructor(promptEditor) {
    this.promptEditor = promptEditor;
  }

  /**
   * 分析订单内容进行提示词优化 - 使用Gemini Pro模型
   */
  async analyzeOrder() {
    const input = document.getElementById('order-analysis-input');
    const aiOptimizeBtn = document.getElementById('ai-optimize-btn');

    if (!input || !input.value.trim()) {
      alert('请先输入订单内容');
      return;
    }

    const orderContent = input.value.trim();
    const channel = document.getElementById('channel-select')?.value || 'generic';

    try {
      // 更新按钮状态
      aiOptimizeBtn.disabled = true;
      aiOptimizeBtn.innerHTML = '🔄 AI分析中...';
      aiOptimizeBtn.style.background = '#6c757d';

      console.log('🤖 开始AI提示词优化分析...', {
        channel: channel,
        contentLength: orderContent.length
      });

      // 收集当前渠道的所有字段片段
      const currentFieldSnippets = this.promptEditor.collectCurrentFieldSnippets(channel);

      // 获取字段全集
      const fragmentManager = this.promptEditor.getService('promptFragmentManager');
      const coreFields = fragmentManager ? fragmentManager.getCoreFieldList() :
                        Object.keys(this.promptEditor.fieldDefinitions);

      // 构造AI提示词
      const aiPrompt = this.buildOptimizationPrompt(orderContent, currentFieldSnippets, coreFields, channel);

      // 调用Gemini Pro模型
      const geminiService = this.promptEditor.getService('gemini');
      if (!geminiService) {
        throw new Error('Gemini服务不可用');
      }

      // 获取Pro模型名称
      const proModel = geminiService.getDefaultProModel ?
                      geminiService.getDefaultProModel() :
                      'gemini-2.5-pro';

      const result = await geminiService.callGeminiAPIWithRetry(aiPrompt, {
        model: proModel // 使用配置的Pro模型进行提示词优化
      });

      if (result.success) {
        // 解析AI响应
        const suggestions = this.parseAISuggestions(result.content);

        if (suggestions) {
          // 存储建议供预览使用
          this.promptEditor._lastAISuggestions = suggestions;

          // 显示建议预览
          this.showAISuggestionsPreview(suggestions);

          console.log('✅ AI优化分析完成', {
            suggestionsCount: Object.keys(suggestions.field_suggestions || {}).length
          });
        } else {
          throw new Error('AI响应解析失败');
        }
      } else {
        throw new Error(result.error || 'AI分析失败');
      }

    } catch (error) {
      console.error('❌ AI优化分析失败:', error);
      alert('AI分析失败: ' + error.message + '\n\n请检查网络连接或稍后重试');

    } finally {
      // 恢复按钮状态
      aiOptimizeBtn.disabled = false;
      aiOptimizeBtn.innerHTML = '🚀 AI生成字段建议';
      aiOptimizeBtn.style.background = 'linear-gradient(45deg, #ffc107, #e0a800)';
    }
  }

  /**
   * 构造AI优化提示词
   */
  buildOptimizationPrompt(orderContent, currentSnippets, coreFields, channel) {
    const prompt = `You are an expert prompt optimization assistant. Analyze the order content and optimize field extraction prompts.

TASK: Based on the order content and current field prompts, generate optimized field extraction prompts.

CURRENT CHANNEL: ${channel}

CORE FIELDS TO CONSIDER:
${coreFields.map(field => `- ${field}`).join('\n')}

CURRENT FIELD PROMPTS:
${Object.keys(currentSnippets).length > 0 ?
  Object.entries(currentSnippets).map(([field, prompt]) =>
    `${field}: ${prompt.substring(0, 200)}${prompt.length > 200 ? '...' : ''}`
  ).join('\n\n') :
  'No current field prompts available.'
}

ORDER CONTENT TO ANALYZE:
---
${orderContent}
---

REQUIREMENTS:
1. Analyze the order content to understand the data structure and extraction patterns
2. For each relevant field, provide an optimized extraction prompt
3. Focus on fields that appear in the order content or are commonly needed
4. Make prompts specific, clear, and effective for extraction
5. Consider the channel context (${channel}) for platform-specific patterns

OUTPUT FORMAT (JSON only, no explanations):
{
  "channel": "${channel}",
  "field_suggestions": {
    "field_name": {
      "prompt": "optimized extraction prompt",
      "rationale": "brief explanation why this prompt is better"
    }
  },
  "notes": "optional global suggestions"
}

Generate optimized prompts for the most relevant fields based on the order content.`;

    return prompt;
  }

  /**
   * 解析AI建议响应
   */
  parseAISuggestions(aiResponse) {
    try {
      // 尝试直接解析JSON
      const suggestions = JSON.parse(aiResponse);

      if (suggestions && suggestions.field_suggestions) {
        return suggestions;
      } else {
        console.warn('AI响应缺少field_suggestions字段');
        return null;
      }

    } catch (error) {
      console.warn('直接JSON解析失败，尝试提取JSON结构:', error);

      // 尝试提取JSON结构
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const suggestions = JSON.parse(jsonMatch[0]);
          if (suggestions && suggestions.field_suggestions) {
            return suggestions;
          }
        } catch (extractError) {
          console.error('提取的JSON解析失败:', extractError);
        }
      }

      console.error('AI响应解析完全失败:', aiResponse.substring(0, 500));
      return null;
    }
  }

  /**
   * 显示AI建议预览对话框
   */
  showAISuggestionsPreview(suggestions) {
    const fieldSuggestions = suggestions.field_suggestions || {};
    const notes = suggestions.notes || '';

    if (Object.keys(fieldSuggestions).length === 0) {
      alert('AI没有生成任何字段建议');
      return;
    }

    // 创建预览模态框
    const modal = document.createElement('div');
    modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.6); display: flex; justify-content: center; align-items: center; z-index: 1001;';

    const container = document.createElement('div');
    container.style.cssText = 'background: white; border-radius: 8px; max-width: 900px; max-height: 80vh; overflow-y: auto; padding: 0;';

    // 生成字段建议HTML
    const suggestionsHtml = Object.entries(fieldSuggestions).map(([field, suggestion]) => {
      const isRequired = this.promptEditor.requiredFields.includes(field);
      const fieldColor = isRequired ? '#dc3545' : '#28a745';
      const fieldIcon = isRequired ? '🔴' : '🔵';

      return `
        <div style="margin-bottom: 15px; padding: 15px; border: 1px solid ${fieldColor}; border-radius: 6px; background: #f8f9fa;">
          <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
            <input type="checkbox" id="suggest-${field}" checked style="transform: scale(1.2);">
            <span>${fieldIcon}</span>
            <strong style="color: ${fieldColor};">${field}</strong>
            <span style="font-size: 12px; color: #6c757d;">${isRequired ? '(必填)' : '(可选)'}</span>
          </div>
          <div style="margin-bottom: 8px;">
            <strong>建议提示词:</strong>
            <div style="background: white; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 100px; overflow-y: auto;">
              ${suggestion.prompt || '无建议'}
            </div>
          </div>
          <div style="font-size: 12px; color: #6c757d;">
            <strong>优化理由:</strong> ${suggestion.rationale || '无说明'}
          </div>
        </div>
      `;
    }).join('');

    container.innerHTML = `
      <div style="padding: 20px; border-bottom: 1px solid #dee2e6;">
        <h4 style="margin: 0; color: #495057; display: flex; align-items: center; gap: 8px;">
          🤖 AI字段优化建议
          <span style="font-size: 12px; background: #ffc107; color: #212529; padding: 2px 8px; border-radius: 12px;">
            ${Object.keys(fieldSuggestions).length} 个字段
          </span>
        </h4>
        ${notes ? `<div style="margin-top: 10px; padding: 10px; background: #e3f2fd; border-radius: 4px; font-size: 14px;">${notes}</div>` : ''}
      </div>

      <div style="padding: 20px; max-height: 400px; overflow-y: auto;">
        <div style="margin-bottom: 15px; display: flex; gap: 10px; justify-content: center;">
          <button onclick="document.querySelectorAll('[id^=suggest-]').forEach(cb => cb.checked = true)"
                  style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
            ✓ 全选
          </button>
          <button onclick="document.querySelectorAll('[id^=suggest-]').forEach(cb => cb.checked = false)"
                  style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
            ✗ 全不选
          </button>
        </div>

        ${suggestionsHtml}
      </div>

      <div style="padding: 20px; border-top: 1px solid #dee2e6; text-align: center; display: flex; gap: 10px; justify-content: center;">
        <button id="apply-suggestions-btn" style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: bold;">
          ✅ 应用选中建议
        </button>
        <button onclick="this.closest('.modal').remove()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer;">
          ❌ 取消
        </button>
      </div>
    `;

    modal.className = 'modal';
    modal.appendChild(container);
    document.body.appendChild(modal);

    // 绑定应用建议按钮事件
    const applyBtn = container.querySelector('#apply-suggestions-btn');
    applyBtn.addEventListener('click', () => {
      const selectedSuggestions = {};

      // 收集选中的建议
      Object.keys(fieldSuggestions).forEach(field => {
        const checkbox = document.getElementById(`suggest-${field}`);
        if (checkbox && checkbox.checked) {
          selectedSuggestions[field] = fieldSuggestions[field];
        }
      });

      if (Object.keys(selectedSuggestions).length === 0) {
        alert('请至少选择一个字段建议');
        return;
      }

      // 应用选中的建议
      this.applyOptimizationResults(selectedSuggestions);

      // 关闭模态框
      modal.remove();
    });

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) modal.remove();
    });
  }

  /**
   * 应用优化建议到字段
   */
  applyOptimizationResults(selectedSuggestions) {
    let appliedCount = 0;

    for (const [field, suggestion] of Object.entries(selectedSuggestions)) {
      const textarea = document.getElementById(`content-${field}`);

      if (textarea && suggestion.prompt) {
        // 应用建议到字段
        textarea.value = suggestion.prompt;

        // 触发内容变化事件
        this.promptEditor.onFieldContentChange(field);

        // 视觉反馈
        textarea.style.background = '#d1ecf1';
        setTimeout(() => {
          textarea.style.background = 'white';
        }, 2000);

        appliedCount++;
        console.log(`✅ 已应用AI建议到字段: ${field}`);
      }
    }

    if (appliedCount > 0) {
      alert(`✅ 已应用 ${appliedCount} 个字段的AI优化建议\n\n请检查字段内容，然后点击"保存所有字段"进行持久化保存`);

      // 刷新预览
      this.promptEditor.updateComposedPreview();
    } else {
      alert('没有成功应用任何建议');
    }
  }
}

window.promptEditor = new PromptEditor();
function openPromptEditor() { window.promptEditor.openEditor(); }

/**
 * 简化的系统验证函数 - 供开发者快速检查系统状态
 */
window.validateFieldModularSystem = function() {
  console.log('🔍 开始字段模块化系统验证...');

  const results = {
    promptEditor: !!window.promptEditor,
    aiOptimizer: !!(window.promptEditor && window.promptEditor.aiOptimizer),
    getService: !!window.getService,
    geminiService: !!window.getService('gemini'),
    fragmentManager: !!window.getService('promptFragmentManager'),
    universalTemplates: false,
    coreFields: false
  };

  // 检查通用模板
  try {
    const fragmentManager = window.getService('promptFragmentManager');
    if (fragmentManager && fragmentManager.getUniversalFieldTemplates) {
      const templates = fragmentManager.getUniversalFieldTemplates();
      results.universalTemplates = Object.keys(templates).length > 0;
    }

    if (fragmentManager && fragmentManager.getCoreFieldList) {
      const fields = fragmentManager.getCoreFieldList();
      results.coreFields = Array.isArray(fields) && fields.length > 0;
    }
  } catch (error) {
    console.warn('模板检查失败:', error);
  }

  // 输出结果
  console.log('📊 验证结果:');
  Object.entries(results).forEach(([key, value]) => {
    console.log(`  ${value ? '✅' : '❌'} ${key}: ${value}`);
  });

  const passCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;

  console.log(`\n🎯 总体结果: ${passCount}/${totalCount} 通过`);

  if (passCount === totalCount) {
    console.log('🎉 系统验证通过！字段模块化功能正常。');
  } else {
    console.log('⚠️ 部分功能异常，请检查相关模块。');
  }

  return { passCount, totalCount, success: passCount === totalCount, results };
};

console.log('💡 使用 validateFieldModularSystem() 验证系统状态');