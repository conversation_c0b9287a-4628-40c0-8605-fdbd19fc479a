/**
 * 统一静态数据源 - 渠道检测编辑器（浏览器全局版）
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（纯数据文件）
 * 被依赖：channel-detector.js, field-mapper.js, app.js, rule-editor.js, prompt-editor.js
 * 全局变量：创建多个 window.* 全局数据对象
 * 数据提供：渠道配置、车型信息、区域设置、语言配置、服务类型、工具函数
 * 
 * === 核心功能 ===
 * - 渠道检测规则配置（CHANNEL_DETECTION_RULES）
 * - 完整渠道列表（COMPLETE_CHANNEL_LIST）
 * - 车型推荐规则（VEHICLE_RECOMMENDATION_RULES） 
 * - 服务类型映射（SERVICE_TYPE_KEYWORDS）
 * - 统一工具函数集合（DataUtils）
 * 
 * === 使用场景 ===
 * - channel-detector.js 使用检测规则和关键词映射
 * - field-mapper.js 使用车型推荐和服务类型检测
 * - rule-editor.js 管理渠道规则配置
 * - prompt-editor.js 获取渠道列表用于同步
 * 
 * === 注意事项 ===
 * 该文件为系统数据基础，所有业务逻辑模块都依赖此文件的数据定义
 * 修改数据结构会影响整个系统，需谨慎操作
 * 使用IIFE模式避免全局命名空间污染
 * 
 * 将原先的 ES Module 导出改为挂载到 window，便于通过 <script> 直接引用。
 */
(function () {
  'use strict';

  // ==================== 渠道配置 ====================
  
  // 完整渠道列表
  window.COMPLETE_CHANNEL_LIST = [
    // 核心OTA平台
    'Klook West Malaysia', 'Klook Singapore', 'Kkday', 'Ctrip West Malaysia', 'Ctrip API',
    '携程专车', '携程商铺 - CN', 'Fliggy', 'Traveloka', 'Heycar', 'Mozio',

    // SMW相关渠道
    'SMW Eric', 'Smw Wilson', 'Smw Josua', 'Smw Jcyap', 'Smw Vivian Lim', 'Smw Wendy', 
    'Smw Annie', 'SMW Xiaohongshu', 'SMW Whatsapp', 'SMW Agent', 'SMW Walk In', 'SMW Driver Walk-In Com',

    // GMH团队渠道
    'GMH Sabah', '随程-GMH Sabah', 'GMH Terry', 'GMH Ms Yong', 'GMH Ashley', 'GMH Calvin',
    'GMH May', 'GMH Daniel Fong', 'GMH BNI', 'GMH SQ', 'GMH Jiahui', 'GMH Vikki', 'GMH Qijun',
    'GMH Venus', 'GMH Karen', 'GMH Cynthia B10', 'GMH Cynthia', 'GMH Jing Soon', 'GMH Driver',
    'GMH Xiaoxuan', 'GMH Vivian B2B', 'GMH Ads', 'GoMyHire - KL', 'GoMyHire Webpage', 'Gomyhire Pohchengfatt',

    // JR Coach Services系列
    'JR Coach Credit', 'JR Coach Cash',
    'JR COACH SERVICES - C1', 'JR COACH SERVICES - HTP - C1', 'JR COACH SERVICES - GTV - C1',
    'JR COACH SERVICES - JRV - C1', 'JR COACH SERVICES - WYNN - C1', 'JR COACH SERVICES - EJH - C1',

    // 酒店合作伙伴
    'Hotel - Padibox Homestay', 'Hotel - Padi Sentral Homestay', 'Hotel - Secret Garden Homestay',
    'Hotel - Leshore Hotel', 'Hotel - VI Boutique', 'Hotel - East Sun Hotel', 'The Pearl Kuala Lumpur Hotel',
    'Le Méridien Putrajaya', 'ONE18 Boutique Hotel', 'Bintang Collectionz Hotel',

    // MapleHome系列
    'MapleHome - The Robertson KL', 'MapleHome - Swiss Garden Kuala Lumpur',
    "MapleHome - D'Majestic Premier Suites Kuala Lumpur", 'MapleHome- Chambers Premier Suites Kuala Lumpur',
    'MapleHome - Geo38 Premier Suites Kuala Lumpur', 'MapleHome - The Apple Premier Suites Melaka',
    'MapleHome - Amber Cove Premier Suites Melaka', 'The Maple Suite - Bukit Bintang',

    // Ocean Blue系列
    'Ocean Blue - JC TRAVEL SDN BHD - TC2', 'Ocean Blue - JC TRAVEL SDN BHD QR',

    // 旅行社和代理商
    'B2B Lewis', 'B TN Holiday Sdn Bhd-Eunice', 'Chong Dealer', 'Jing Ge', 'Jing Ge Htp',
    'YenNei', 'EHTT 徐杰', 'Joydeer', 'KL Eric', 'Co-operate Stan', '7deer Travel', 'Columbia',
    'Asia Trail', 'Good Earth Travel', 'Thousand Travel', 'Sabah Adventure', '全景旅游',
    'M.I.C.E Tour', 'Mytravelexpert - TC1', 'Eramaz Travel C1', '上海佳秞', 'Sativa - ID - (C) HTP',
    '云南昆果教育', 'WelcomePickups Sabah', 'WelcomePickups West Malaysia', 'ReSkill',
    'Want To Eat Restaurant', 'Driver Own Job', 'Smartryde HTP', 'KTMB', 'HTP - 机场嘉华',
    'Reward', 'Bob', 'Pg Sue', 'Pg Afzan', 'KK Lucas', 'Agent Victor', 'JC666', 'Wiracle Vincent',
    'BNI Member', 'PS Member', 'PS Badminton Team & Family',
    'Sim Card', 'SIM Card + Paging', 'Paging', 'Rental', 'Rent To Own', 'Penalty',
    '789 Genting', 'The Little Series', 'Syn', 'CEO Chaffer Premium', 'Link Center (SBH)', 'ATV Borneo Sabah',

    // UCSI系列
    'UCSI - Cheras', 'UCSI - Port Dickson', 'Student Travel',

    // 其他专属渠道
    'Kai - TC1', 'KelvinLim - D1',

    // 财务 / 流程类
    'Refund',

    // 特殊 / 业务追加
    'Sativa - ID - Cynthia HTP',

    // 变体 / 兼容别名
    'Kai - TC', 'JR COACH SERVICES - HTC - C1',

    // 保持通用占位符最后
    'Other'
  ];

  // 渠道检测规则配置
  window.CHANNEL_DETECTION_RULES = {
    // Fliggy渠道检测规则
    fliggy: {
      name: 'Fliggy',
      displayName: '飞猪',
      patterns: [
        '订单编号：19位数字',
        '订单编号:19位数字',
        '订单编号 19位数字',
        '飞猪订单',
        'Fliggy订单'
      ],
      confidence: 0.95,
      description: '阿里巴巴旗下飞猪旅行平台'
    },

    // JingGe渠道检测规则
    jingge: {
      name: 'JingGe',
      displayName: '精格',
      patterns: ['商铺', '精格', 'JingGe', '精格订单'],
      confidence: 0.85,
      description: '精格旅游服务平台'
    },

    // Ctrip渠道检测规则
    ctrip: {
      name: 'Ctrip',
      displayName: '携程',
      patterns: ['携程', 'Ctrip', 'CTRIP', '携程订单', 'Ctrip订单'],
      confidence: 0.9,
      description: '携程旅行网，中国领先的在线旅行服务公司'
    },

    // Klook渠道检测规则
    klook: {
      name: 'Klook',
      displayName: 'Klook',
      patterns: ['Klook', 'KLOOK', '客路', 'Klook订单', '客路订单'],
      confidence: 0.85,
      description: 'Klook客路旅行，亚洲领先的旅游体验平台'
    },

    // Kkday渠道检测规则
    kkday: {
      name: 'Kkday',
      displayName: 'Kkday',
      patterns: [
        'Kkday', 'KKDAY', 'KKday', 'kkday',
        'Kkday订单', 'KKday订单',
        'Skymirror World', 'kkday.com',
        '订单编号.*KK', '\\d{2}KK\\d+',
        'Copyright © KKday'
      ],
      confidence: 0.9,
      description: 'KKday，亚洲当地旅游体验平台'
    },

    // Traveloka渠道检测规则
    traveloka: {
      name: 'Traveloka',
      displayName: 'Traveloka',
      patterns: ['Traveloka', 'TRAVELOKA', 'Traveloka订单'],
      confidence: 0.8,
      description: 'Traveloka，东南亚领先的在线旅游平台'
    },

    // Chong Dealer渠道检测规则
    chongDealer: {
      name: 'ChongDealer',
      displayName: '崇德',
      patterns: ['Chong Dealer', '崇德', 'CHONG', '崇德订单'],
      confidence: 0.9,
      description: '崇德汽车经销商网络'
    }
  };

  // 参考号模式配置
  window.REFERENCE_PATTERNS = {
    CD: {
      channel: 'Chong Dealer',
      displayName: '崇德',
      confidence: 0.95,
      description: '崇德经销商参考号前缀'
    },
    CT: {
      channel: 'Ctrip',
      displayName: '携程',
      confidence: 0.9,
      description: '携程参考号前缀'
    },
    KL: {
      channel: 'Klook West Malaysia',
      displayName: 'Klook马来西亚',
      confidence: 0.9,
      description: 'Klook马来西亚参考号前缀'
    },
    KK: {
      channel: 'Kkday',
      displayName: 'Kkday',
      confidence: 0.9,
      description: 'Kkday参考号前缀'
    }
  };

  // 关键词检测配置
  window.KEYWORD_DETECTION = {
    'chong dealer': {
      channel: 'Chong Dealer',
      displayName: '崇德',
      confidence: 0.9,
      description: '崇德关键词匹配'
    },
    携程: {
      channel: 'Ctrip',
      displayName: '携程',
      confidence: 0.85,
      description: '携程中文关键词匹配'
    },
    ctrip: {
      channel: 'Ctrip',
      displayName: '携程',
      confidence: 0.85,
      description: '携程英文关键词匹配'
    },
    klook: {
      channel: 'Klook West Malaysia',
      displayName: 'Klook马来西亚',
      confidence: 0.85,
      description: 'Klook关键词匹配'
    },
    kkday: {
      channel: 'Kkday',
      displayName: 'Kkday',
      confidence: 0.85,
      description: 'Kkday关键词匹配'
    },
    traveloka: {
      channel: 'Traveloka',
      displayName: 'Traveloka',
      confidence: 0.8,
      description: 'Traveloka关键词匹配'
    }
  };

  // 渠道显示名称映射
  window.CHANNEL_DISPLAY_NAMES = {
    fliggy: '飞猪',
    jingge: '精格',
    ctrip: '携程',
    klook: 'Klook',
    kkday: 'Kkday',
    traveloka: 'Traveloka',
    'Chong Dealer': '崇德',
    'Klook West Malaysia': 'Klook马来西亚'
  };

  // 渠道描述信息
  window.CHANNEL_DESCRIPTIONS = {
    fliggy: '阿里巴巴旗下飞猪旅行平台',
    jingge: '精格旅游服务平台',
    ctrip: '携程旅行网，中国领先的在线旅行服务公司',
    klook: 'Klook客路旅行，亚洲领先的旅游体验平台',
    kkday: 'Kkday，亚洲当地旅游体验平台',
    traveloka: 'Traveloka，东南亚领先的在线旅游平台',
    'Chong Dealer': '崇德汽车经销商网络',
    'Klook West Malaysia': 'Klook马来西亚西部服务'
  };

  // ==================== 车型配置 ====================

  // 完整车型列表
  window.VEHICLE_TYPES = [
    { id: 38, name: '4 Seater Hatchback', displayName: '4座掀背车', description: '3乘客, 2件L尺寸行李', minPassengers: 3, maxPassengers: 3, luggageCapacity: 2 },
    { id: 5, name: '5 Seater', displayName: '5座轿车', description: '3乘客, 3件L尺寸行李', minPassengers: 3, maxPassengers: 3, luggageCapacity: 3 },
    { id: 33, name: 'Premium 5 Seater', displayName: '豪华5座', description: '奔驰/宝马专用 (3乘客, 3件L尺寸行李)', minPassengers: 3, maxPassengers: 3, luggageCapacity: 3 },
    { id: 37, name: 'Extended 5', displayName: '加长5座', description: '4乘客, 4件L尺寸行李', minPassengers: 4, maxPassengers: 4, luggageCapacity: 4 },
    { id: 35, name: '7 Seater SUV', displayName: '7座SUV', description: '4乘客, 4件L尺寸行李', minPassengers: 4, maxPassengers: 4, luggageCapacity: 4 },
    { id: 15, name: '7 Seater MPV', displayName: '7座MPV', description: '5乘客, 4件L尺寸行李', minPassengers: 5, maxPassengers: 5, luggageCapacity: 4 },
    { id: 16, name: 'Standard Size MPV', displayName: '标准MPV', description: '5乘客, 4件L尺寸行李', minPassengers: 5, maxPassengers: 5, luggageCapacity: 4 },
    { id: 31, name: 'Luxury Mpv', displayName: '豪华MPV', description: '日产Serena (5乘客, 4件L尺寸行李)', minPassengers: 5, maxPassengers: 5, luggageCapacity: 4 },
    { id: 32, name: 'Velfire/Alphard', displayName: '埃尔法', description: '6乘客, 4件L尺寸行李', minPassengers: 6, maxPassengers: 6, luggageCapacity: 4 },
    { id: 36, name: 'Alphard', displayName: '阿尔法', description: '6乘客, 4件L尺寸行李', minPassengers: 6, maxPassengers: 6, luggageCapacity: 4 },
    { id: 20, name: '10 Seater MPV', displayName: '10座MPV', description: '7乘客, 7件L尺寸行李', minPassengers: 7, maxPassengers: 7, luggageCapacity: 7 },
    { id: 30, name: '12 seat Starex', displayName: '12座Starex', description: '7乘客, 7件L尺寸行李', minPassengers: 7, maxPassengers: 7, luggageCapacity: 7 },
    { id: 23, name: '14 Seater Van', displayName: '14座面包车', description: '10乘客, 10件L尺寸行李', minPassengers: 10, maxPassengers: 10, luggageCapacity: 10 },
    { id: 24, name: '18 Seater Van', displayName: '18座面包车', description: '12乘客, 12件L尺寸行李', minPassengers: 12, maxPassengers: 12, luggageCapacity: 12 },
    { id: 25, name: '30 Seat Mini Bus', displayName: '30座小巴', description: '29乘客, 29件L尺寸行李', minPassengers: 29, maxPassengers: 29, luggageCapacity: 29 },
    { id: 26, name: '44 Seater Bus', displayName: '44座大巴', description: '43乘客, 43件L尺寸行李', minPassengers: 43, maxPassengers: 43, luggageCapacity: 43 },
    { id: 34, name: 'Ticket', displayName: '门票车', description: 'N/A乘客, N/A行李', minPassengers: 0, maxPassengers: 0, luggageCapacity: 0 },
    { id: 39, name: 'Ticket (Non-Malaysian)', displayName: '门票车(非马来西亚)', description: 'N/A乘客, N/A行李', minPassengers: 0, maxPassengers: 0, luggageCapacity: 0 }
  ];

  // 车型推荐规则
  window.VEHICLE_RECOMMENDATION_RULES = [
    { minPassengers: 1, maxPassengers: 3, carTypeId: 5, name: '5 Seater' },
    { minPassengers: 4, maxPassengers: 4, carTypeId: 37, name: 'Extended 5' },
    { minPassengers: 5, maxPassengers: 5, carTypeId: 15, name: '7 Seater MPV' },
    { minPassengers: 6, maxPassengers: 6, carTypeId: 32, name: 'Velfire/Alphard' },
    { minPassengers: 7, maxPassengers: 7, carTypeId: 20, name: '10 Seater MPV' },
    { minPassengers: 8, maxPassengers: 10, carTypeId: 23, name: '14 Seater Van' }
  ];

  // 特殊车型配置
  window.SPECIAL_VEHICLES = {
    ticket: { carTypeId: 34, name: 'Ticket', displayName: '门票车' },
    hatchback: { carTypeId: 38, name: '4 Seater Hatchback', displayName: '4座掀背车' }
  };

  // ==================== 行驶区域配置 ====================

  // 行驶区域列表
  window.DRIVING_REGIONS = [
    { id: 1, name: 'Kl/selangor', displayName: '吉隆坡/雪兰莪', description: 'KL地区', code: 'KL' },
    { id: 2, name: 'Penang', displayName: '槟城', description: 'PNG地区', code: 'PNG' },
    { id: 3, name: 'Johor', displayName: '柔佛', description: 'JB地区', code: 'JB' },
    { id: 4, name: 'Sabah', displayName: '沙巴', description: 'SBH地区', code: 'SBH' },
    { id: 5, name: 'Singapore', displayName: '新加坡', description: 'SG地区', code: 'SG' },
    { id: 6, name: '携程司导', displayName: '携程司导', description: 'CTRIP专车服务', code: 'CTRIP' },
    { id: 8, name: 'Complete', displayName: '完整服务', description: 'COMPLETE全套服务', code: 'COMPLETE' },
    { id: 9, name: 'Paging', displayName: '传呼服务', description: 'PG传呼服务', code: 'PG' },
    { id: 10, name: 'Charter', displayName: '包车服务', description: 'CHRT包车服务', code: 'CHRT' },
    { id: 12, name: 'Malacca', displayName: '马六甲', description: 'MLK地区', code: 'MLK' },
    { id: 13, name: 'SMW', displayName: 'SMW服务', description: 'SMW专属服务', code: 'SMW' }
  ];

  // ==================== 语言配置 ====================

  // 支持的语言列表
  window.SUPPORTED_LANGUAGES = [
    { id: 2, code: 'en', name: 'English', displayName: '英语', description: 'EN英语服务' },
    { id: 3, code: 'my', name: 'Malay', displayName: '马来语', description: 'MY马来语服务' },
    { id: 4, code: 'cn', name: 'Chinese', displayName: '中文', description: 'CN中文服务' },
    { id: 5, code: 'pg', name: 'Paging', displayName: '传呼服务', description: 'PG传呼服务' },
    { id: 6, code: 'charter', name: 'Charter', displayName: '包车服务', description: 'CHARTER包车服务' },
    { id: 8, code: 'im', name: '携程司导', displayName: '携程司导', description: 'IM携程司导服务' },
    { id: 9, code: 'psv', name: 'PSV', displayName: 'PSV服务', description: 'PSV专业服务' },
    { id: 10, code: 'evp', name: 'EVP', displayName: 'EVP服务', description: 'EVP高级服务' },
    { id: 11, code: 'car', name: 'Car Type Reverify', displayName: '车型复核', description: 'CAR车型复核服务' },
    { id: 12, code: 'jetty', name: 'Jetty', displayName: '码头服务', description: 'JETTY码头服务' },
    { id: 13, code: 'photo', name: 'PhotoSkill Proof', displayName: '摄影证明', description: 'PHOTO摄影证明服务' }
  ];

  // 默认语言配置
  window.DEFAULT_LANGUAGE_CONFIG = {
    defaultLanguageId: 4, // 中文
    defaultLanguageArray: { '0': '4' },
    supportedLanguageIds: [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13]
  };

  // ==================== 订单类型配置 ====================

  // 服务类型（订单类型）
  window.SERVICE_TYPES = [
    { id: 2, name: 'Airport Pickup', displayName: '接机服务', description: '机场接机服务' },
    { id: 3, name: 'Airport Dropoff', displayName: '送机服务', description: '机场送机服务' },
    { id: 4, name: 'Charter', displayName: '包车服务', description: '包车租赁服务' },
    { id: 5, name: 'Hourly Rental', displayName: '小时租赁', description: '按小时计费的租赁服务' },
    { id: 6, name: 'Tour', displayName: '旅游服务', description: '旅游观光服务' },
    { id: 7, name: 'Transfer', displayName: '接送服务', description: '点对点接送服务' }
  ];

  // 服务类型检测关键词
  window.SERVICE_TYPE_KEYWORDS = {
    接机: 2,
    'airport pickup': 2,
    pickup: 2,
    送机: 3,
    'airport dropoff': 3,
    dropoff: 3,
    包车: 4,
    charter: 4,
    小时租赁: 5,
    hourly: 5,
    旅游: 6,
    tour: 6,
    接送: 7,
    transfer: 7
  };

  // ==================== 工具函数 ====================

  // 将纯文本模式转换为正则表达式
  function patternToRegex(patternText) {
    // 处理特殊模式：19位数字订单号
    if (patternText.includes('19位数字')) {
      return /订单编号[：:\s]*\d{19}/;
    }

    // 处理通配符模式
    if (patternText.includes('*') || patternText.includes('?')) {
      let regexPattern = patternText
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.')
        .replace(/\(/g, '\\(')
        .replace(/\)/g, '\\)')
        .replace(/\[/g, '\\[')
        .replace(/\]/g, '\\]');
      return new RegExp(regexPattern, 'i');
    }

    // 普通文本匹配（不区分大小写）
    return new RegExp(patternText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
  }

  function getAllChannelsInfo() {
    return Object.entries(window.CHANNEL_DETECTION_RULES).map(([key, config]) => ({
      id: key,
      name: config.name,
      displayName: config.displayName,
      description: config.description,
      confidence: config.confidence,
      patterns: config.patterns
    }));
  }

  function getChannelDisplayName(channelId) {
    return window.CHANNEL_DISPLAY_NAMES[channelId] || channelId;
  }

  function getChannelDescription(channelId) {
    return window.CHANNEL_DESCRIPTIONS[channelId] || '未知渠道';
  }

  function getVehicleTypeById(vehicleId) {
    return (
      window.VEHICLE_TYPES.find((vehicle) => vehicle.id === vehicleId) || {
        id: vehicleId,
        name: 'Unknown',
        displayName: '未知车型',
        description: '未知车型'
      }
    );
  }

  function getDrivingRegionById(regionId) {
    return (
      window.DRIVING_REGIONS.find((region) => region.id === regionId) || {
        id: regionId,
        name: 'Unknown',
        displayName: '未知区域',
        description: '未知区域'
      }
    );
  }

  function getLanguageById(languageId) {
    return (
      window.SUPPORTED_LANGUAGES.find((lang) => lang.id === languageId) || {
        id: languageId,
        name: 'Unknown',
        displayName: '未知语言',
        description: '未知语言'
      }
    );
  }

  function getServiceTypeById(serviceId) {
    return (
      window.SERVICE_TYPES.find((service) => service.id === serviceId) || {
        id: serviceId,
        name: 'Unknown',
        displayName: '未知服务',
        description: '未知服务类型'
      }
    );
  }

  // 暴露工具集
  window.DataUtils = {
    patternToRegex,
    getAllChannelsInfo,
    getChannelDisplayName,
    getChannelDescription,
    getVehicleTypeById,
    getDrivingRegionById,
    getLanguageById,
    getServiceTypeById
  };
})();