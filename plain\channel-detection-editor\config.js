/**
 * 配置管理模块 - 模块化重构版本
 * 
 * 重构更新：
 * - 消除全局变量污染 (window.configManager, window.OTA.config)
 * - 实现工厂函数模式，支持依赖注入
 * - 保持向后兼容的功能接口
 * - 移除隐式全局命名空间依赖
 * 
 * 设计原则：
 * - 单一职责：仅负责配置管理逻辑
 * - 显式依赖：通过参数明确声明依赖关系  
 * - 可测试性：支持独立单元测试
 * - 接口稳定：保持原有API不变
 */
class ConfigManager {
    constructor() {
        this.config = this.loadDefaultConfig();
        this.initializeConfig();
    }

    /**
     * 加载默认配置
     */
    loadDefaultConfig() {
        return {
            // 完整用户账号列表
            COMPLETE_USER_LIST: [
                // 系统管理员
                { id: 1, name: 'Super Admin', email: '', phone: '', role_id: 1 },

                // 核心用户
                { id: 37, name: 'smw', email: '<EMAIL>', phone: '0162234711', role_id: 2 },
                { id: 420, name: 'chong<PERSON><PERSON><PERSON>', email: '<EMAIL>', phone: '0167112699', role_id: 2 },
                { id: 533, name: 'xhs', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 1201, name: 'KK Lucas', email: '<EMAIL>', phone: '+601153392333', role_id: 2 },
                { id: 2446, name: 'UCSI - Cheras', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 2666, name: 'JRCoach', email: '<EMAIL>', phone: '', role_id: 2 },

                // 新增活跃用户
                { id: 2793, name: 'eramaztravel', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 2788, name: 'kai -JB', email: '<EMAIL>', phone: '+60167878373', role_id: 2 },
                { id: 2766, name: 'demo', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 2765, name: 'Mytravelexpert', email: '<EMAIL>', phone: '+60167788740', role_id: 2 },
                { id: 2732, name: 'oceanblue', email: '<EMAIL>', phone: '+60127697117', role_id: 2 },
                { id: 2847, name: 'KelvinLim', email: '<EMAIL>', phone: '', role_id: 2 },

                // 现有用户
                { id: 89, name: 'GMH Sabah', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 310, name: 'Jcy', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 312, name: 'opVenus', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 313, name: 'opEric', email: '', phone: '', role_id: 2 },
                { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
                { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
                { id: 421, name: 'josua', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 428, name: 'Gomyhire Yong', email: '', phone: '', role_id: 2 },
                { id: 550, name: 'test', email: '', phone: '', role_id: 2 },
                { id: 622, name: 'CsBob', email: '', phone: '', role_id: 2 },
                { id: 777, name: '空空', email: '空空@gomyhire.com', phone: '', role_id: 2 },
                { id: 812, name: '淼淼', email: '', phone: '', role_id: 2 },
                { id: 856, name: 'GMH Ashley', email: '', phone: '', role_id: 2 },
                { id: 907, name: 'OP XINYIN', email: '', phone: '', role_id: 2 },
                { id: 1043, name: 'Billy Yong close', email: '', phone: '', role_id: 2 },
                { id: 1047, name: 'OP QiJun', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 1181, name: 'Op Karen', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 1223, name: 'Chong admin', email: '', phone: '', role_id: 2 },
                { id: 1652, name: 'CSteam Swee Qing', email: 'Swee <EMAIL>', phone: '', role_id: 2 },
                { id: 1832, name: 'GMH SG William', email: '', phone: '', role_id: 2 },
                { id: 2050, name: 'agent victor', email: '', phone: '', role_id: 2 },
                { id: 2085, name: 'CSteam Tze Ying', email: '', phone: '', role_id: 2 },
                { id: 2141, name: 'SMW Nas', email: '', phone: '', role_id: 2 },
                { id: 2142, name: 'SMW Wen', email: '', phone: '', role_id: 2 },
                { id: 2248, name: 'GMH Shi Wei', email: '', phone: '', role_id: 2 },
                { id: 2249, name: 'Skymirror jetty', email: 'Skymirror <EMAIL>', phone: '', role_id: 2 },
                { id: 2340, name: 'GMH JingSoon', email: '', phone: '', role_id: 2 },
                { id: 2358, name: 'GMH Zilok', email: '', phone: '', role_id: 2 },
                { id: 2503, name: 'GMH Veron', email: '', phone: '', role_id: 2 },
                { id: 2595, name: 'Admin Pua', email: '', phone: '', role_id: 2 }
            ],

            // 完整OTA渠道列表
            COMPLETE_CHANNEL_LIST: [
                // 核心OTA平台
                'Klook West Malaysia', 'Klook Singapore', 'Kkday', 'Ctrip West Malaysia', 'Ctrip API',
                '携程专车', '携程商铺 - CN', 'Fliggy', 'Traveloka', 'Heycar', 'Mozio',

                // SMW相关渠道
                'SMW Eric', 'Smw Wilson', 'Smw Josua', 'Smw Jcyap', 'Smw Vivian Lim', 'Smw Wendy', 
                'Smw Annie', 'SMW Xiaohongshu', 'SMW Whatsapp', 'SMW Agent', 'SMW Walk In', 'SMW Driver Walk-In Com',

                // GMH团队渠道
                'GMH Sabah', '随程-GMH Sabah', 'GMH Terry', 'GMH Ms Yong', 'GMH Ashley', 'GMH Calvin',
                'GMH May', 'GMH Daniel Fong', 'GMH BNI', 'GMH SQ', 'GMH Jiahui', 'GMH Vikki', 'GMH Qijun',
                'GMH Venus', 'GMH Karen', 'GMH Cynthia B10', 'GMH Cynthia', 'GMH Jing Soon', 'GMH Driver',
                'GMH Xiaoxuan', 'GMH Vivian B2B', 'GMH Ads', 'GoMyHire - KL', 'GoMyHire Webpage', 'Gomyhire Pohchengfatt',

                // JR Coach Services系列
                'JR Coach Credit', 'JR Coach Cash',
                'JR COACH SERVICES - C1', 'JR COACH SERVICES - HTP - C1', 'JR COACH SERVICES - GTV - C1',
                'JR COACH SERVICES - JRV - C1', 'JR COACH SERVICES - WYNN - C1', 'JR COACH SERVICES - EJH - C1',

                // 酒店合作伙伴
                'Hotel - Padibox Homestay', 'Hotel - Padi Sentral Homestay', 'Hotel - Secret Garden Homestay',
                'Hotel - Leshore Hotel', 'Hotel - VI Boutique', 'Hotel - East Sun Hotel', 'The Pearl Kuala Lumpur Hotel',
                'Le Méridien Putrajaya', 'ONE18 Boutique Hotel', 'Bintang Collectionz Hotel',

                // MapleHome系列
                'MapleHome - The Robertson KL', 'MapleHome - Swiss Garden Kuala Lumpur',
                'MapleHome - D\'Majestic Premier Suites Kuala Lumpur', 'MapleHome- Chambers Premier Suites Kuala Lumpur',
                'MapleHome - Geo38 Premier Suites Kuala Lumpur', 'MapleHome - The Apple Premier Suites Melaka',
                'MapleHome - Amber Cove Premier Suites Melaka', 'The Maple Suite - Bukit Bintang',

                // Ocean Blue系列
                'Ocean Blue - JC TRAVEL SDN BHD - TC2', 'Ocean Blue - JC TRAVEL SDN BHD QR',

                // 旅行社和代理商
                'B2B Lewis', 'B TN Holiday Sdn Bhd-Eunice', 'Chong Dealer', 'Jing Ge', 'Jing Ge Htp',
                'YenNei', 'EHTT 徐杰', 'Joydeer', 'KL Eric', 'Co-operate Stan', '7deer Travel', 'Columbia',
                'Asia Trail', 'Good Earth Travel', 'Thousand Travel', 'Sabah Adventure', '全景旅游',
                'M.I.C.E Tour', 'Mytravelexpert - TC1', 'Eramaz Travel C1', '上海佳禾', 'Sativa - ID - (C) HTP',
                '云南昆果教育', 'WelcomePickups Sabah', 'WelcomePickups West Malaysia', 'ReSkill',
                'Want To Eat Restaurant', 'Driver Own Job', 'Smartryde HTP', 'KTMB', 'HTP - 空港嘉华',
                'Reward', 'Bob', 'Pg Sue', 'Pg Afzan', 'KK Lucas', 'Agent Victor', 'JC666', 'Wiracle Vincent',
                'BNI Member', 'PS Member', 'PS Badminton Team & Family',
                'Sim Card', 'SIM Card + Paging', 'Paging', 'Rental', 'Rent To Own', 'Penalty',
                '789 Genting', 'The Little Series', 'Syn', 'CEO Chaffer Premium', 'Link Center (SBH)', 'ATV Borneo Sabah',
                'diagnosis-test',

                // UCSI系列
                'UCSI - Cheras', 'UCSI - Port Dickson', 'Student Travel',

                // 其他专属渠道
                'Kai - TC1', 'Demo', 'KelvinLim - D1',

                // 测试 / 部署验证渠道
                'final_deployment_test', 'success_test', 'Klook test', 'Heycar test', 'KKDAY SGD',

                // 财务 / 流程类
                'Refund',

                // 特殊 / 业务追加
                'Sativa - ID - Cynthia HTP',

                // 变体 / 兼容别名
                'Kai - TC', 'JR COACH SERVICES - HTC - C1',

                // 保持通用占位符最后
                'Other'
            ],

            // 用户权限配置
            USER_PERMISSION_CONFIG: {
                restrictedUsers: {
                    // <EMAIL> (ID: 2793)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Eramaz Travel C1'] }
                    },
                    2793: {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Eramaz Travel C1'] }
                    },

                    // <EMAIL> (ID: 2788)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Kai - TC1'] }
                    },
                    2788: {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Kai - TC1'] }
                    },

                    // <EMAIL> (ID: 2766)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { restricted: true, allowedChannels: ['Demo'] }
                    },
                    2766: {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { restricted: true, allowedChannels: ['Demo'] }
                    },

                    // <EMAIL> (ID: 2765)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Mytravelexpert - TC1'] }
                    },
                    2765: {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Mytravelexpert - TC1'] }
                    },

                    // <EMAIL> (ID: 2732)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Ocean Blue - JC TRAVEL SDN BHD - TC2'] }
                    },
                    2732: {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['Ocean Blue - JC TRAVEL SDN BHD - TC2'] }
                    },

                    // <EMAIL> (ID: 2666)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { 
                            restricted: true,
                            defaultChannel: 'JR Coach Credit',
                            allowedChannels: [
                                'JR Coach Credit', 'JR Coach Cash',
                                'JR COACH SERVICES - C1', 'JR COACH SERVICES - HTP - C1',
                                'JR COACH SERVICES - GTV - C1', 'JR COACH SERVICES - JRV - C1',
                                'JR COACH SERVICES - WYNN - C1', 'JR COACH SERVICES - EJH - C1'
                            ]
                        }
                    },
                    2666: {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { 
                            restricted: true,
                            defaultChannel: 'JR Coach Credit',
                            allowedChannels: [
                                'JR Coach Credit', 'JR Coach Cash',
                                'JR COACH SERVICES - C1', 'JR COACH SERVICES - HTP - C1',
                                'JR COACH SERVICES - GTV - C1', 'JR COACH SERVICES - JRV - C1',
                                'JR COACH SERVICES - WYNN - C1', 'JR COACH SERVICES - EJH - C1'
                            ]
                        }
                    },

                    // <EMAIL> (ID: 2446)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['UCSI - Cheras', 'UCSI - Port Dickson'] }
                    },
                    2446: {
                        priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['UCSI - Cheras', 'UCSI - Port Dickson'] }
                    },

                    // <EMAIL> (ID: 420)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { restricted: true, allowedChannels: ['Chong Dealer'] }
                    },
                    420: {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { restricted: true, allowedChannels: ['Chong Dealer'] }
                    },

                    // 空空@gomyhire.com (ID: 777)
                    '空空@gomyhire.com': {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { restricted: true, allowedChannels: ['Fliggy', 'Jing Ge'] }
                    },
                    777: {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: true },
                        channels: { restricted: true, allowedChannels: ['Fliggy', 'Jing Ge'] }
                    },

                    // <EMAIL> (ID: 2847)
                    '<EMAIL>': {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['KelvinLim - D1'] }
                    },
                    2847: {
                        priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                        features: { canUsePaging: false },
                        channels: { restricted: true, allowedChannels: ['KelvinLim - D1'] }
                    }
                },

                defaultPermissions: {
                    priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                    features: { canUsePaging: true },
                    channels: { restricted: false, allowedChannels: null }
                },

                config: {
                    enabled: true,
                    caseSensitive: false,
                    debugMode: false,
                    cacheTimeout: 5 * 60 * 1000,
                    version: '2.0.0',
                    lastUpdated: '2025-08-12'
                }
            },

            // 权限模板
            PERMISSION_TEMPLATES: {
                FULLY_RESTRICTED: {
                    priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                    features: { canUsePaging: false },
                    channels: { restricted: true, allowedChannels: [] }
                },
                PRICE_RESTRICTED: {
                    priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
                    features: { canUsePaging: true },
                    channels: { restricted: false, allowedChannels: null }
                },
                CHANNEL_RESTRICTED: {
                    priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                    features: { canUsePaging: true },
                    channels: { restricted: true, allowedChannels: [] }
                },
                FULL_ACCESS: {
                    priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
                    features: { canUsePaging: true },
                    channels: { restricted: false, allowedChannels: null }
                }
            }
        };
    }

    /**
     * 初始化配置（重构版本 - 移除全局变量污染）
     */
    initializeConfig() {
        // 移除全局命名空间创建，改为内部初始化
        console.log('✅ 配置管理器已初始化（模块化版本）');
    }

    /**
     * 规范化用户标识
     */
    normalizeUserIdentifier(userIdentifier) {
        if (typeof userIdentifier === 'string') {
            return this.config.USER_PERMISSION_CONFIG.config.caseSensitive 
                ? userIdentifier 
                : userIdentifier.toLowerCase();
        }
        return userIdentifier;
    }

    /**
     * 获取用户权限配置
     */
    getUserPermissions(userIdentifier) {
        if (!this.config.USER_PERMISSION_CONFIG.config.enabled) {
            return this.config.USER_PERMISSION_CONFIG.defaultPermissions;
        }

        const normalizedId = this.normalizeUserIdentifier(userIdentifier);
        
        if (this.config.USER_PERMISSION_CONFIG.restrictedUsers[normalizedId]) {
            return this.config.USER_PERMISSION_CONFIG.restrictedUsers[normalizedId];
        }

        return this.config.USER_PERMISSION_CONFIG.defaultPermissions;
    }

    /**
     * 检查用户是否有特定权限
     */
    hasPermission(userIdentifier, permissionPath) {
        const userPermissions = this.getUserPermissions(userIdentifier);
        
        const pathParts = permissionPath.split('.');
        let value = userPermissions;
        
        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return false;
            }
        }
        
        return Boolean(value);
    }

    /**
     * 获取用户允许的渠道列表
     */
    getAllowedChannels(userIdentifier) {
        const userPermissions = this.getUserPermissions(userIdentifier);
        
        if (!userPermissions.channels.restricted) {
            return null;
        }
        
        return userPermissions.channels.allowedChannels || [];
    }

    /**
     * 获取用户的默认渠道
     */
    getDefaultChannel(userIdentifier) {
        const userPermissions = this.getUserPermissions(userIdentifier);
        const allowedChannels = this.getAllowedChannels(userIdentifier);

        if (userPermissions.channels && userPermissions.channels.defaultChannel) {
            return userPermissions.channels.defaultChannel;
        }

        if (allowedChannels && allowedChannels.length > 0) {
            return allowedChannels[0];
        }

        return this.config.COMPLETE_CHANNEL_LIST[0] || null;
    }

    /**
     * 根据邮箱查找用户
     */
    findUserByEmail(email) {
        const normalizedEmail = email.toLowerCase();
        return this.config.COMPLETE_USER_LIST.find(user => 
            user.email && user.email.toLowerCase() === normalizedEmail
        );
    }

    /**
     * 根据ID查找用户
     */
    findUserById(id) {
        return this.config.COMPLETE_USER_LIST.find(user => user.id === id);
    }

    /**
     * 获取所有用户列表
     */
    getAllUsers() {
        return this.config.COMPLETE_USER_LIST;
    }

    /**
     * 获取所有渠道列表
     */
    getAllChannels() {
        return this.config.COMPLETE_CHANNEL_LIST;
    }

    /**
     * 检查渠道是否存在
     */
    channelExists(channelName) {
        return this.config.COMPLETE_CHANNEL_LIST.includes(channelName);
    }

    /**
     * 添加新渠道
     */
    addChannel(channelName) {
        if (!this.channelExists(channelName)) {
            this.config.COMPLETE_CHANNEL_LIST.push(channelName);
            return true;
        }
        return false;
    }

    /**
     * 导出当前配置
     */
    exportConfig() {
        return JSON.parse(JSON.stringify(this.config));
    }

    /**
     * 导入配置
     */
    importConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.initializeConfig();
        return true;
    }
}

// 模块工厂函数 - 替代全局实例创建
function createConfigModule() {
    return new ConfigManager();
}

// 向后兼容支持 - 为现有代码提供过渡期
if (typeof window !== 'undefined' && !window.moduleContainer) {
    console.warn('⚠️  检测到传统模式，创建兼容实例');
    window.configManager = createConfigModule();
    
    // 临时全局命名空间支持
    window.OTA = window.OTA || {};
    window.OTA.config = window.configManager.config;
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('config', createConfigModule, []);
    console.log('📦 ConfigManager已注册到模块容器');
}