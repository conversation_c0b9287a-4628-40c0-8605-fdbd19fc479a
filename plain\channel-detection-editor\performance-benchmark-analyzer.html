<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能基准分析器 - 缓存效果量化评估</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .content-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 800px;
        }
        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 30px 20px;
        }
        .main-content {
            padding: 30px;
            overflow-y: auto;
        }
        .benchmark-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }
        .benchmark-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 8px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        .metric-value {
            font-size: 2.5em;
            font-weight: 700;
            color: #4facfe;
            margin-bottom: 8px;
            line-height: 1;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .metric-trend {
            font-size: 0.8em;
            margin-top: 5px;
        }
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        .performance-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #4facfe;
        }
        .comparison-card.before { border-left-color: #dc3545; }
        .comparison-card.after { border-left-color: #28a745; }
        .test-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 20px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 6px;
            transition: width 0.5s ease;
            width: 0%;
        }
        .log-panel {
            background: #1a1a1a;
            color: #e5e5e5;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .nav-button {
            display: block;
            width: 100%;
            padding: 12px 16px;
            margin-bottom: 8px;
            border: none;
            border-radius: 8px;
            background: white;
            color: #495057;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .nav-button:hover, .nav-button.active {
            background: #4facfe;
            color: white;
            transform: translateX(4px);
        }
        .benchmark-data {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .data-point {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            border-left: 3px solid #4facfe;
        }
        .data-point.improvement {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .data-point.degradation {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .section {
            display: none;
        }
        .section.active {
            display: block;
        }
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 性能基准分析器</h1>
            <p>量化评估架构优化后的性能提升 - 缓存命中率 | 响应时间 | 内存优化 | 用户体验</p>
        </div>

        <div class="content-grid">
            <div class="sidebar">
                <button class="nav-button active" onclick="showSection('overview')">
                    📊 性能概览
                </button>
                <button class="nav-button" onclick="showSection('cache-performance')">
                    💾 缓存性能
                </button>
                <button class="nav-button" onclick="showSection('response-time')">
                    ⚡ 响应时间
                </button>
                <button class="nav-button" onclick="showSection('memory-analysis')">
                    🧠 内存分析
                </button>
                <button class="nav-button" onclick="showSection('benchmark-comparison')">
                    📈 基准对比
                </button>
                <button class="nav-button" onclick="showSection('real-time-monitoring')">
                    🔄 实时监控
                </button>
            </div>

            <div class="main-content">
                <!-- 性能概览 -->
                <div id="overview" class="section active">
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value" id="overall-improvement">--</div>
                            <div class="metric-label">整体性能提升</div>
                            <div class="metric-trend trend-stable" id="overall-trend">待测量</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="cache-hit-rate-display">--</div>
                            <div class="metric-label">缓存命中率</div>
                            <div class="metric-trend trend-stable" id="cache-trend">待测量</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="avg-response-time-display">--</div>
                            <div class="metric-label">平均响应时间</div>
                            <div class="metric-trend trend-stable" id="response-trend">待测量</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="memory-usage-display">--</div>
                            <div class="metric-label">内存使用优化</div>
                            <div class="metric-trend trend-stable" id="memory-trend">待测量</div>
                        </div>
                    </div>

                    <div class="test-controls">
                        <button class="btn-primary" onclick="runComprehensiveBenchmark()">🚀 运行综合基准测试</button>
                        <button class="btn-secondary" onclick="runQuickBenchmark()">⚡ 快速性能测试</button>
                        <button class="btn-success" onclick="startContinuousMonitoring()">📊 开始连续监控</button>
                        <button class="btn-warning" onclick="exportBenchmarkData()">📁 导出数据</button>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" id="benchmark-progress"></div>
                    </div>

                    <div class="log-panel" id="main-log">
                        性能基准分析器已就绪，点击上方按钮开始性能测试...
                    </div>
                </div>

                <!-- 缓存性能 -->
                <div id="cache-performance" class="section">
                    <div class="benchmark-section">
                        <h3>缓存系统性能指标</h3>
                        
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" id="gemini-cache-hits">--</div>
                                <div class="metric-label">Gemini API缓存命中</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="address-cache-hits">--</div>
                                <div class="metric-label">地址翻译缓存命中</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="channel-cache-hits">--</div>
                                <div class="metric-label">渠道检测缓存命中</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="total-cache-size">--</div>
                                <div class="metric-label">缓存占用空间</div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <h4>缓存命中率趋势</h4>
                            <canvas id="cache-trend-chart" width="600" height="300"></canvas>
                        </div>

                        <div class="test-controls">
                            <button class="btn-primary" onclick="testCachePerformance()">测试缓存性能</button>
                            <button class="btn-secondary" onclick="clearAllCaches()">清空缓存</button>
                            <button class="btn-warning" onclick="analyzeCacheEfficiency()">分析缓存效率</button>
                        </div>
                    </div>
                </div>

                <!-- 响应时间 -->
                <div id="response-time" class="section">
                    <div class="benchmark-section">
                        <h3>响应时间性能分析</h3>

                        <div class="performance-comparison">
                            <div class="comparison-card before">
                                <h4>优化前基准</h4>
                                <div class="benchmark-data" id="before-benchmarks">
                                    <div class="data-point">
                                        <strong>字段映射:</strong> <span id="field-mapping-before">--ms</span>
                                    </div>
                                    <div class="data-point">
                                        <strong>渠道检测:</strong> <span id="channel-detection-before">--ms</span>
                                    </div>
                                    <div class="data-point">
                                        <strong>地址翻译:</strong> <span id="address-translation-before">--ms</span>
                                    </div>
                                </div>
                            </div>
                            <div class="comparison-card after">
                                <h4>优化后性能</h4>
                                <div class="benchmark-data" id="after-benchmarks">
                                    <div class="data-point improvement">
                                        <strong>字段映射:</strong> <span id="field-mapping-after">--ms</span>
                                    </div>
                                    <div class="data-point improvement">
                                        <strong>渠道检测:</strong> <span id="channel-detection-after">--ms</span>
                                    </div>
                                    <div class="data-point improvement">
                                        <strong>地址翻译:</strong> <span id="address-translation-after">--ms</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <h4>响应时间对比图</h4>
                            <canvas id="response-time-chart" width="600" height="300"></canvas>
                        </div>

                        <div class="test-controls">
                            <button class="btn-primary" onclick="measureResponseTimes()">测量响应时间</button>
                            <button class="btn-secondary" onclick="runStressTest()">压力测试</button>
                            <button class="btn-success" onclick="compareWithBaseline()">基准对比</button>
                        </div>
                    </div>
                </div>

                <!-- 内存分析 -->
                <div id="memory-analysis" class="section">
                    <div class="benchmark-section">
                        <h3>内存使用分析</h3>

                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" id="heap-usage">--</div>
                                <div class="metric-label">堆内存使用</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="global-vars-count">--</div>
                                <div class="metric-label">全局变量数量</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="module-count">--</div>
                                <div class="metric-label">模块实例数</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="memory-efficiency">--</div>
                                <div class="metric-label">内存效率</div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <h4>内存使用趋势</h4>
                            <canvas id="memory-usage-chart" width="600" height="300"></canvas>
                        </div>

                        <div class="test-controls">
                            <button class="btn-primary" onclick="analyzeMemoryUsage()">分析内存使用</button>
                            <button class="btn-secondary" onclick="forceGarbageCollection()">强制垃圾回收</button>
                            <button class="btn-warning" onclick="detectMemoryLeaks()">检测内存泄漏</button>
                        </div>
                    </div>
                </div>

                <!-- 基准对比 -->
                <div id="benchmark-comparison" class="section">
                    <div class="benchmark-section">
                        <h3>性能基准对比</h3>

                        <div class="benchmark-data" id="comparison-results">
                            <!-- 对比结果将动态生成 -->
                        </div>

                        <div class="chart-container">
                            <h4>性能提升雷达图</h4>
                            <canvas id="performance-radar-chart" width="600" height="400"></canvas>
                        </div>

                        <div class="test-controls">
                            <button class="btn-primary" onclick="runFullComparison()">完整对比测试</button>
                            <button class="btn-secondary" onclick="loadHistoricalData()">加载历史数据</button>
                            <button class="btn-success" onclick="generateReport()">生成性能报告</button>
                        </div>
                    </div>
                </div>

                <!-- 实时监控 -->
                <div id="real-time-monitoring" class="section">
                    <div class="benchmark-section">
                        <h3>实时性能监控</h3>

                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" id="real-time-calls">0</div>
                                <div class="metric-label">实时调用次数</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="real-time-avg-time">--</div>
                                <div class="metric-label">实时平均时间</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="real-time-cache-hits">--</div>
                                <div class="metric-label">实时缓存命中</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="real-time-errors">0</div>
                                <div class="metric-label">错误计数</div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <h4>实时性能图表</h4>
                            <canvas id="real-time-chart" width="600" height="300"></canvas>
                        </div>

                        <div class="test-controls">
                            <button class="btn-success" id="monitoring-toggle" onclick="toggleRealTimeMonitoring()">开始监控</button>
                            <button class="btn-secondary" onclick="resetCounters()">重置计数器</button>
                            <button class="btn-warning" onclick="exportMonitoringData()">导出监控数据</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载所有系统脚本 -->
    <script src="module-container.js"></script>
    <script src="cache-manager.js"></script>
    <script src="cache-integration-adapter.js"></script>
    <script src="cache-monitor-panel.js"></script>
    <script src="error-handler.js"></script>
    <script src="crypto-utils.js"></script>
    <script src="local-storage-manager.js"></script>
    <script src="data.js"></script>
    <script src="../hotels_by_region.js"></script>
    <script src="airport-data.js"></script>
    <script src="config.js"></script>
    <script src="gemini-config.js"></script>
    <script src="prompt-segmenter.js"></script>
    <script src="prompt-composer.js"></script>
    <script src="address-translator.js"></script>
    <script src="channel-detector.js"></script>
    <script src="field-mapper.js"></script>
    <script src="rule-editor.js"></script>
    <script src="prompt-editor.js"></script>
    <script src="app.js"></script>

    <script>
        // 全局状态管理
        let benchmarkState = {
            isMonitoring: false,
            isRunningBenchmark: false,
            monitoringInterval: null,
            baselineData: {},
            currentData: {},
            historicalData: []
        };

        // 日志函数
        function log(message, level = 'info') {
            const logPanel = document.getElementById('main-log');
            const timestamp = new Date().toLocaleTimeString();
            
            const colors = {
                info: '#e5e5e5',
                success: '#4ade80',
                warning: '#fbbf24',
                error: '#ef4444',
                data: '#60a5fa'
            };
            
            logPanel.innerHTML += `<div style="color: ${colors[level]};">[${timestamp}] ${message}</div>`;
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 页面导航
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelectorAll('.nav-button').forEach(button => {
                button.classList.remove('active');
            });
            
            document.getElementById(sectionId).classList.add('active');
            event.target.classList.add('active');
        }

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('benchmark-progress').style.width = `${percentage}%`;
        }

        // 初始化系统
        document.addEventListener('DOMContentLoaded', async function() {
            log('🔧 性能基准分析器初始化中...', 'info');
            
            try {
                if (window.moduleContainer) {
                    await window.moduleContainer.initialize();
                    log('✅ 模块容器初始化完成', 'success');
                    
                    // 检查缓存系统
                    if (window.moduleContainer.has('cacheManager')) {
                        log('💾 缓存系统已启用', 'success');
                    } else {
                        log('⚠️ 缓存系统未找到', 'warning');
                    }
                    
                    // 初始化集成适配器
                    if (window.moduleContainer.has('cacheIntegrationAdapter')) {
                        const adapter = window.moduleContainer.get('cacheIntegrationAdapter');
                        
                        // 为核心模块启用缓存增强
                        const fieldMapper = window.moduleContainer.get('fieldMapper');
                        const addressTranslator = window.moduleContainer.get('addressTranslator');
                        const channelDetector = window.moduleContainer.get('channelDetector');
                        
                        if (fieldMapper && adapter.enhanceFieldMapper) {
                            adapter.enhanceFieldMapper(fieldMapper);
                            log('🚀 FieldMapper缓存增强已启用', 'success');
                        }
                        
                        if (addressTranslator && adapter.enhanceAddressTranslator) {
                            adapter.enhanceAddressTranslator(addressTranslator);
                            log('🌐 AddressTranslator缓存增强已启用', 'success');
                        }
                        
                        if (channelDetector && adapter.enhanceChannelDetector) {
                            adapter.enhanceChannelDetector(channelDetector);
                            log('🔍 ChannelDetector缓存增强已启用', 'success');
                        }
                    }
                } else {
                    log('⚠️ 传统模式运行，缓存增强可能不可用', 'warning');
                }
                
                // 初始化显示
                updatePerformanceMetrics();
                
                log('🎉 性能基准分析器就绪！', 'success');
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
            }
        });

        // 运行综合基准测试
        async function runComprehensiveBenchmark() {
            if (benchmarkState.isRunningBenchmark) {
                log('⚠️ 基准测试已在进行中', 'warning');
                return;
            }
            
            benchmarkState.isRunningBenchmark = true;
            log('🚀 开始综合性能基准测试...', 'info');
            
            const testSuites = [
                { name: '缓存系统性能', func: testCachePerformance, weight: 25 },
                { name: '响应时间测量', func: measureResponseTimes, weight: 30 },
                { name: '内存使用分析', func: analyzeMemoryUsage, weight: 20 },
                { name: '并发压力测试', func: runStressTest, weight: 15 },
                { name: '用户体验指标', func: measureUserExperience, weight: 10 }
            ];
            
            let completedWeight = 0;
            const totalWeight = testSuites.reduce((sum, suite) => sum + suite.weight, 0);
            
            try {
                for (const suite of testSuites) {
                    updateProgress((completedWeight / totalWeight) * 100);
                    log(`📊 执行 ${suite.name}...`, 'info');
                    
                    try {
                        const result = await suite.func();
                        log(`✅ ${suite.name} 完成 - ${result.summary}`, 'success');
                    } catch (error) {
                        log(`❌ ${suite.name} 失败: ${error.message}`, 'error');
                    }
                    
                    completedWeight += suite.weight;
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                updateProgress(100);
                log('🎉 综合基准测试完成！', 'success');
                
                // 更新所有指标显示
                updatePerformanceMetrics();
                generatePerformanceReport();
                
            } catch (error) {
                log(`💥 基准测试执行失败: ${error.message}`, 'error');
            } finally {
                benchmarkState.isRunningBenchmark = false;
            }
        }

        // 快速性能测试
        async function runQuickBenchmark() {
            log('⚡ 执行快速性能测试...', 'info');
            
            try {
                // 快速响应时间测试
                const responseTimeResult = await quickResponseTimeTest();
                
                // 快速缓存测试
                const cacheResult = await quickCacheTest();
                
                // 快速内存检查
                const memoryResult = quickMemoryCheck();
                
                // 更新主要指标
                updateMainMetrics({
                    overallImprovement: responseTimeResult.improvement,
                    cacheHitRate: cacheResult.hitRate,
                    avgResponseTime: responseTimeResult.avgTime,
                    memoryUsage: memoryResult.usage
                });
                
                log(`✅ 快速测试完成 - 整体提升 ${responseTimeResult.improvement}`, 'success');
                
            } catch (error) {
                log(`❌ 快速测试失败: ${error.message}`, 'error');
            }
        }

        // 快速响应时间测试
        async function quickResponseTimeTest() {
            const testData = '客户：测试用户\n电话：+86-13800138000\n订单：1234567890123456789';
            const iterations = 5;
            const times = [];
            
            try {
                let fieldMapper = null;
                if (window.moduleContainer && window.moduleContainer.has('fieldMapper')) {
                    fieldMapper = window.moduleContainer.get('fieldMapper');
                } else if (window.fieldMapper) {
                    fieldMapper = window.fieldMapper;
                }
                
                if (!fieldMapper) {
                    return { improvement: 'N/A', avgTime: 'N/A' };
                }
                
                for (let i = 0; i < iterations; i++) {
                    const startTime = performance.now();
                    await fieldMapper.processCompleteData(testData);
                    const endTime = performance.now();
                    times.push(endTime - startTime);
                    
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
                
                const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
                const firstTime = times[0];
                const lastTime = times[times.length - 1];
                const improvement = firstTime > lastTime ? 
                    `${((firstTime - lastTime) / firstTime * 100).toFixed(1)}%` : '0%';
                
                return {
                    improvement,
                    avgTime: `${avgTime.toFixed(2)}ms`,
                    times
                };
                
            } catch (error) {
                return { improvement: 'Error', avgTime: 'Error' };
            }
        }

        // 快速缓存测试
        async function quickCacheTest() {
            try {
                let cacheManager = null;
                if (window.moduleContainer && window.moduleContainer.has('cacheManager')) {
                    cacheManager = window.moduleContainer.get('cacheManager');
                } else if (window.cacheManager) {
                    cacheManager = window.cacheManager;
                }
                
                if (!cacheManager) {
                    return { hitRate: 'N/A' };
                }
                
                // 执行一些缓存操作
                const testKey = 'quick-test-' + Date.now();
                const testValue = { data: 'test', timestamp: Date.now() };
                
                await cacheManager.set(testKey, testValue, 30000);
                const retrieved = await cacheManager.get(testKey);
                
                const stats = cacheManager.getStats();
                
                return {
                    hitRate: stats.hitRate || '0%',
                    stats
                };
                
            } catch (error) {
                return { hitRate: 'Error' };
            }
        }

        // 快速内存检查
        function quickMemoryCheck() {
            const memoryInfo = window.performance && window.performance.memory ? {
                used: Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(window.performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(window.performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : null;
            
            const globalVarCount = Object.keys(window).filter(key => 
                key.toLowerCase().includes('channel') ||
                key.toLowerCase().includes('field') ||
                key.toLowerCase().includes('cache')
            ).length;
            
            return {
                usage: memoryInfo ? `${memoryInfo.used}MB` : 'N/A',
                globalVars: globalVarCount,
                efficiency: memoryInfo ? `${((1 - memoryInfo.used / memoryInfo.limit) * 100).toFixed(1)}%` : 'N/A'
            };
        }

        // 更新主要性能指标
        function updateMainMetrics(metrics) {
            document.getElementById('overall-improvement').textContent = metrics.overallImprovement || '--';
            document.getElementById('cache-hit-rate-display').textContent = metrics.cacheHitRate || '--';
            document.getElementById('avg-response-time-display').textContent = metrics.avgResponseTime || '--';
            document.getElementById('memory-usage-display').textContent = metrics.memoryUsage || '--';
            
            // 更新趋势指示器
            updateTrendIndicators(metrics);
        }

        // 更新趋势指示器
        function updateTrendIndicators(metrics) {
            const overallTrend = document.getElementById('overall-trend');
            const cacheTrend = document.getElementById('cache-trend');
            const responseTrend = document.getElementById('response-trend');
            const memoryTrend = document.getElementById('memory-trend');
            
            // 模拟趋势计算（实际应用中应基于历史数据）
            if (metrics.overallImprovement && metrics.overallImprovement !== 'N/A') {
                overallTrend.textContent = '↗ 持续改善';
                overallTrend.className = 'metric-trend trend-up';
            }
            
            if (metrics.cacheHitRate && parseFloat(metrics.cacheHitRate) > 50) {
                cacheTrend.textContent = '↗ 效率提升';
                cacheTrend.className = 'metric-trend trend-up';
            }
            
            if (metrics.avgResponseTime && parseFloat(metrics.avgResponseTime) < 100) {
                responseTrend.textContent = '↗ 响应优秀';
                responseTrend.className = 'metric-trend trend-up';
            }
        }

        // 缓存性能测试
        async function testCachePerformance() {
            log('💾 开始缓存性能测试...', 'info');
            
            try {
                let cacheManager = null;
                let integrationAdapter = null;
                
                if (window.moduleContainer) {
                    cacheManager = window.moduleContainer.has('cacheManager') ? 
                        window.moduleContainer.get('cacheManager') : null;
                    integrationAdapter = window.moduleContainer.has('cacheIntegrationAdapter') ?
                        window.moduleContainer.get('cacheIntegrationAdapter') : null;
                } else {
                    cacheManager = window.cacheManager;
                    integrationAdapter = window.cacheIntegrationAdapter;
                }
                
                if (!cacheManager) {
                    return { summary: '缓存管理器不可用' };
                }
                
                const testCases = [
                    { key: 'gemini-test-1', data: '客户：张三\n电话：13800138000' },
                    { key: 'address-test-1', data: '吉隆坡国际机场' },
                    { key: 'channel-test-1', data: '订单编号：1234567890123456789' }
                ];
                
                const results = {
                    cacheWrites: 0,
                    cacheReads: 0,
                    hitCount: 0,
                    missCount: 0,
                    totalTime: 0
                };
                
                for (const testCase of testCases) {
                    // 写入测试
                    const writeStart = performance.now();
                    await cacheManager.set(testCase.key, testCase.data, 300000);
                    results.totalTime += performance.now() - writeStart;
                    results.cacheWrites++;
                    
                    // 读取测试（应该命中）
                    const readStart = performance.now();
                    const retrieved = await cacheManager.get(testCase.key);
                    results.totalTime += performance.now() - readStart;
                    results.cacheReads++;
                    
                    if (retrieved === testCase.data) {
                        results.hitCount++;
                    } else {
                        results.missCount++;
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
                
                // 获取缓存统计
                const stats = cacheManager.getStats();
                
                // 更新缓存指标显示
                document.getElementById('gemini-cache-hits').textContent = stats.hits || 0;
                document.getElementById('total-cache-size').textContent = `${stats.bytesStoredMB || 0}MB`;
                
                log(`📊 缓存测试数据: 命中率 ${stats.hitRate}, 大小 ${stats.bytesStoredMB}MB`, 'data');
                
                return {
                    summary: `缓存命中率 ${stats.hitRate}, 平均时间 ${(results.totalTime / (results.cacheWrites + results.cacheReads)).toFixed(2)}ms`,
                    details: results
                };
                
            } catch (error) {
                log(`❌ 缓存性能测试失败: ${error.message}`, 'error');
                return { summary: '测试失败' };
            }
        }

        // 响应时间测量
        async function measureResponseTimes() {
            log('⚡ 开始响应时间测量...', 'info');
            
            const testSuites = [
                { name: '字段映射', test: measureFieldMappingTime },
                { name: '渠道检测', test: measureChannelDetectionTime },
                { name: '地址翻译', test: measureAddressTranslationTime }
            ];
            
            const results = {};
            
            try {
                for (const suite of testSuites) {
                    const result = await suite.test();
                    results[suite.name] = result;
                    
                    // 更新UI显示
                    const elementId = suite.name === '字段映射' ? 'field-mapping-after' :
                                    suite.name === '渠道检测' ? 'channel-detection-after' :
                                    'address-translation-after';
                    
                    document.getElementById(elementId).textContent = `${result.avgTime.toFixed(2)}ms`;
                    
                    log(`📏 ${suite.name}: 平均 ${result.avgTime.toFixed(2)}ms`, 'data');
                }
                
                const overallAvg = Object.values(results).reduce((sum, r) => sum + r.avgTime, 0) / Object.keys(results).length;
                
                return {
                    summary: `平均响应时间 ${overallAvg.toFixed(2)}ms`,
                    details: results
                };
                
            } catch (error) {
                log(`❌ 响应时间测量失败: ${error.message}`, 'error');
                return { summary: '测量失败' };
            }
        }

        // 字段映射时间测量
        async function measureFieldMappingTime() {
            const testData = '客户：李先生\n电话：+86-13900139000\n订单：KL-ABC123456\n航班：CZ123';
            const iterations = 10;
            const times = [];
            
            let fieldMapper = null;
            if (window.moduleContainer && window.moduleContainer.has('fieldMapper')) {
                fieldMapper = window.moduleContainer.get('fieldMapper');
            } else if (window.fieldMapper) {
                fieldMapper = window.fieldMapper;
            }
            
            if (!fieldMapper) {
                return { avgTime: 0, times: [] };
            }
            
            for (let i = 0; i < iterations; i++) {
                const startTime = performance.now();
                await fieldMapper.processCompleteData(testData);
                const endTime = performance.now();
                times.push(endTime - startTime);
                
                await new Promise(resolve => setTimeout(resolve, 20));
            }
            
            return {
                avgTime: times.reduce((sum, time) => sum + time, 0) / times.length,
                times
            };
        }

        // 渠道检测时间测量
        async function measureChannelDetectionTime() {
            const testCases = [
                '订单编号：1234567890123456789 飞猪平台',
                'KL-ABC123456 Klook预订',
                'CD-DEF789012 携程订单',
                'KK-GHI345678 KKday预订'
            ];
            const times = [];
            
            let channelDetector = null;
            if (window.moduleContainer && window.moduleContainer.has('channelDetector')) {
                channelDetector = window.moduleContainer.get('channelDetector');
            } else if (window.channelDetector) {
                channelDetector = window.channelDetector;
            }
            
            if (!channelDetector) {
                return { avgTime: 0, times: [] };
            }
            
            for (const testCase of testCases) {
                for (let i = 0; i < 3; i++) {
                    const startTime = performance.now();
                    channelDetector.detectChannel(testCase);
                    const endTime = performance.now();
                    times.push(endTime - startTime);
                }
            }
            
            return {
                avgTime: times.reduce((sum, time) => sum + time, 0) / times.length,
                times
            };
        }

        // 地址翻译时间测量
        async function measureAddressTranslationTime() {
            const addresses = [
                '吉隆坡国际机场',
                'KLIA Terminal 1',
                '双威酒店',
                '新加坡樟宜机场T3'
            ];
            const times = [];
            
            let addressTranslator = null;
            if (window.moduleContainer && window.moduleContainer.has('addressTranslator')) {
                addressTranslator = window.moduleContainer.get('addressTranslator');
            } else if (window.addressTranslator) {
                addressTranslator = window.addressTranslator;
            }
            
            if (!addressTranslator) {
                return { avgTime: 0, times: [] };
            }
            
            try {
                for (const address of addresses) {
                    const startTime = performance.now();
                    await addressTranslator.translateAddress(address);
                    const endTime = performance.now();
                    times.push(endTime - startTime);
                    
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
                
                return {
                    avgTime: times.reduce((sum, time) => sum + time, 0) / times.length,
                    times
                };
            } catch (error) {
                return { avgTime: 0, times: [] };
            }
        }

        // 内存使用分析
        async function analyzeMemoryUsage() {
            log('🧠 开始内存使用分析...', 'info');
            
            const memoryInfo = window.performance && window.performance.memory ? {
                used: window.performance.memory.usedJSHeapSize,
                total: window.performance.memory.totalJSHeapSize,
                limit: window.performance.memory.jsHeapSizeLimit
            } : null;
            
            // 统计全局变量
            const globalVars = Object.keys(window).filter(key => 
                key.toLowerCase().includes('channel') ||
                key.toLowerCase().includes('field') ||
                key.toLowerCase().includes('cache') ||
                key.toLowerCase().includes('gemini') ||
                key.toLowerCase().includes('config')
            );
            
            // 统计模块实例
            let moduleCount = 0;
            if (window.moduleContainer && window.moduleContainer.modules) {
                moduleCount = window.moduleContainer.modules.size;
            }
            
            // 更新内存指标显示
            if (memoryInfo) {
                document.getElementById('heap-usage').textContent = `${Math.round(memoryInfo.used / 1024 / 1024)}MB`;
                document.getElementById('memory-efficiency').textContent = `${((1 - memoryInfo.used / memoryInfo.limit) * 100).toFixed(1)}%`;
            }
            
            document.getElementById('global-vars-count').textContent = globalVars.length;
            document.getElementById('module-count').textContent = moduleCount;
            
            log(`💾 内存分析: ${Math.round(memoryInfo?.used / 1024 / 1024 || 0)}MB, ${globalVars.length}个全局变量`, 'data');
            
            return {
                summary: `内存使用 ${Math.round(memoryInfo?.used / 1024 / 1024 || 0)}MB, ${globalVars.length}个全局变量`,
                details: {
                    memoryInfo,
                    globalVars: globalVars.length,
                    moduleCount
                }
            };
        }

        // 压力测试
        async function runStressTest() {
            log('💪 开始并发压力测试...', 'info');
            
            const concurrentCalls = 50;
            const testData = '压力测试数据：客户张三，电话13800138000';
            const promises = [];
            const startTime = performance.now();
            
            try {
                let fieldMapper = null;
                if (window.moduleContainer && window.moduleContainer.has('fieldMapper')) {
                    fieldMapper = window.moduleContainer.get('fieldMapper');
                } else if (window.fieldMapper) {
                    fieldMapper = window.fieldMapper;
                }
                
                if (!fieldMapper) {
                    return { summary: '字段映射器不可用' };
                }
                
                for (let i = 0; i < concurrentCalls; i++) {
                    promises.push(fieldMapper.processCompleteData(`${testData} #${i}`));
                }
                
                await Promise.all(promises);
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                const avgTime = totalTime / concurrentCalls;
                
                log(`🔥 压力测试完成: ${concurrentCalls}次并发调用，总时间${totalTime.toFixed(2)}ms`, 'data');
                
                return {
                    summary: `${concurrentCalls}次并发调用，平均${avgTime.toFixed(2)}ms/次`,
                    details: {
                        concurrentCalls,
                        totalTime,
                        avgTime
                    }
                };
                
            } catch (error) {
                log(`❌ 压力测试失败: ${error.message}`, 'error');
                return { summary: '压力测试失败' };
            }
        }

        // 用户体验指标测量
        async function measureUserExperience() {
            log('👤 测量用户体验指标...', 'info');
            
            const metrics = {
                firstContentfulPaint: 0,
                largestContentfulPaint: 0,
                cumulativeLayoutShift: 0,
                firstInputDelay: 0
            };
            
            // 模拟用户体验指标（实际应用中应使用Performance Observer）
            try {
                if ('performance' in window && 'getEntriesByType' in performance) {
                    const paintEntries = performance.getEntriesByType('paint');
                    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
                    if (fcp) metrics.firstContentfulPaint = fcp.startTime;
                }
                
                // 模拟其他指标
                metrics.largestContentfulPaint = 800; // 模拟值
                metrics.cumulativeLayoutShift = 0.05; // 模拟值
                metrics.firstInputDelay = 20; // 模拟值
                
                return {
                    summary: `FCP: ${metrics.firstContentfulPaint.toFixed(0)}ms, FID: ${metrics.firstInputDelay}ms`,
                    details: metrics
                };
                
            } catch (error) {
                return { summary: '用户体验指标获取失败' };
            }
        }

        // 更新性能指标显示
        function updatePerformanceMetrics() {
            // 在页面加载后定期更新显示
            setTimeout(async () => {
                try {
                    const quickResult = await quickResponseTimeTest();
                    const cacheResult = await quickCacheTest();
                    const memoryResult = quickMemoryCheck();
                    
                    updateMainMetrics({
                        overallImprovement: quickResult.improvement,
                        cacheHitRate: cacheResult.hitRate,
                        avgResponseTime: quickResult.avgTime,
                        memoryUsage: memoryResult.usage
                    });
                } catch (error) {
                    log(`⚠️ 指标更新失败: ${error.message}`, 'warning');
                }
            }, 2000);
        }

        // 生成性能报告
        function generatePerformanceReport() {
            log('📊 生成性能报告...', 'info');
            
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    overallImprovement: document.getElementById('overall-improvement').textContent,
                    cacheHitRate: document.getElementById('cache-hit-rate-display').textContent,
                    avgResponseTime: document.getElementById('avg-response-time-display').textContent,
                    memoryUsage: document.getElementById('memory-usage-display').textContent
                },
                environment: {
                    userAgent: navigator.userAgent,
                    viewport: { width: window.innerWidth, height: window.innerHeight }
                }
            };
            
            benchmarkState.currentData = report;
            log('✅ 性能报告已生成', 'success');
        }

        // 实时监控相关函数
        function toggleRealTimeMonitoring() {
            const button = document.getElementById('monitoring-toggle');
            
            if (!benchmarkState.isMonitoring) {
                startContinuousMonitoring();
                button.textContent = '停止监控';
                button.className = 'btn-secondary';
            } else {
                stopContinuousMonitoring();
                button.textContent = '开始监控';
                button.className = 'btn-success';
            }
        }

        function startContinuousMonitoring() {
            if (benchmarkState.isMonitoring) return;
            
            benchmarkState.isMonitoring = true;
            log('📊 开始实时性能监控...', 'info');
            
            benchmarkState.monitoringInterval = setInterval(async () => {
                try {
                    // 更新实时指标
                    const quickResult = await quickResponseTimeTest();
                    const cacheResult = await quickCacheTest();
                    
                    // 更新实时显示
                    const currentCalls = parseInt(document.getElementById('real-time-calls').textContent) + 1;
                    document.getElementById('real-time-calls').textContent = currentCalls;
                    document.getElementById('real-time-avg-time').textContent = quickResult.avgTime;
                    document.getElementById('real-time-cache-hits').textContent = cacheResult.hitRate;
                    
                } catch (error) {
                    const currentErrors = parseInt(document.getElementById('real-time-errors').textContent) + 1;
                    document.getElementById('real-time-errors').textContent = currentErrors;
                }
            }, 3000);
        }

        function stopContinuousMonitoring() {
            if (!benchmarkState.isMonitoring) return;
            
            benchmarkState.isMonitoring = false;
            
            if (benchmarkState.monitoringInterval) {
                clearInterval(benchmarkState.monitoringInterval);
                benchmarkState.monitoringInterval = null;
            }
            
            log('⏹️ 实时监控已停止', 'info');
        }

        // 导出基准数据
        function exportBenchmarkData() {
            const data = {
                metadata: {
                    title: '性能基准测试数据',
                    timestamp: new Date().toISOString(),
                    version: '2.0.0'
                },
                currentMetrics: benchmarkState.currentData,
                baseline: benchmarkState.baselineData,
                historical: benchmarkState.historicalData,
                environment: {
                    userAgent: navigator.userAgent,
                    performance: window.performance && window.performance.memory ? {
                        usedJSHeapSize: window.performance.memory.usedJSHeapSize,
                        totalJSHeapSize: window.performance.memory.totalJSHeapSize
                    } : null
                }
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance-benchmark-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📁 基准数据已导出', 'success');
        }

        // 其他辅助函数
        function clearAllCaches() {
            try {
                let cacheManager = null;
                if (window.moduleContainer && window.moduleContainer.has('cacheManager')) {
                    cacheManager = window.moduleContainer.get('cacheManager');
                } else if (window.cacheManager) {
                    cacheManager = window.cacheManager;
                }
                
                if (cacheManager && cacheManager.clear) {
                    cacheManager.clear();
                    log('🧹 所有缓存已清空', 'info');
                } else {
                    log('⚠️ 缓存管理器不可用', 'warning');
                }
            } catch (error) {
                log(`❌ 清空缓存失败: ${error.message}`, 'error');
            }
        }

        function resetCounters() {
            document.getElementById('real-time-calls').textContent = '0';
            document.getElementById('real-time-avg-time').textContent = '--';
            document.getElementById('real-time-cache-hits').textContent = '--';
            document.getElementById('real-time-errors').textContent = '0';
            
            log('🔄 计数器已重置', 'info');
        }
    </script>
</body>
</html>