/**
 * 智能地址翻译模块 - 核心翻译引擎
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：hotels_by_region.js（酒店数据源）, airport-data.js（机场数据源）
 * 被依赖：field-mapper.js（字段映射处理）
 * 全局变量：创建 window.addressTranslator 实例
 * 数据处理：酒店名称翻译、机场地址翻译、智能匹配算法
 * 
 * === 核心功能 ===
 * - 基于酒店数据库的地址翻译
 * - 机场名称智能识别和翻译
 * - 多层匹配算法（完整匹配、模糊匹配、关键词匹配）
 * - 智能过滤机制（排除通用地名、品牌连锁等）
 * - 置信度评估和结果排序
 * 
 * === 使用场景 ===
 * - pickup和destination字段的智能翻译
 * - 订单地址信息的标准化处理
 * - 中英文地址的双向识别和转换
 * - 地址翻译质量评估和优化
 * 
 * === 集成点 ===
 * - field-mapper.js：在Gemini处理后调用翻译功能
 * - app.js：显示翻译结果和置信度信息
 * 
 * @DEPENDENCY hotels_by_region.js, airport-data.js
 * @INIT 依赖酒店和机场数据初始化后创建实例
 * @DECLARATION 声明翻译算法和匹配策略
 * @SERVICE 提供地址翻译核心服务
 */

class AddressTranslator {
    constructor() {
        // 等待依赖数据加载
        this.hotelData = null;
        this.airportData = null;
        this.translationCache = new Map();
        this.initialized = false;
        
        // 智能过滤配置
        this.filterConfig = {
            // 通用地名过滤
            genericLocations: [
                '市中心', '机场', '车站', '码头', '港口', '广场', '公园',
                'city center', 'downtown', 'airport', 'station', 'port', 'square', 'park',
                '附近', '周边', '地区', '区域', '一带',
                'nearby', 'around', 'area', 'region', 'vicinity'
            ],
            
            // 品牌连锁酒店过滤
            brandChains: [
                '希尔顿', '万豪', '香格里拉', '洲际', '凯悦', '喜来登', '威斯汀',
                'hilton', 'marriott', 'shangri-la', 'intercontinental', 'hyatt', 
                'sheraton', 'westin', 'holiday inn', 'ibis', 'novotel'
            ],
            
            // 宽泛描述过滤
            broadDescriptions: [
                '酒店', '宾馆', '旅馆', '民宿', '客栈',
                'hotel', 'inn', 'lodge', 'guesthouse', 'resort',
                '大厦', '大楼', '中心', '广场',
                'tower', 'building', 'center', 'plaza'
            ],
            
            // 最小长度限制
            minLength: 3,
            maxLength: 100
        };
        
        // 匹配策略配置
        this.matchingConfig = {
            exactMatch: { weight: 1.0, minConfidence: 0.95 },
            highSimilarity: { weight: 0.9, minConfidence: 0.80 },
            keywordMatch: { weight: 0.7, minConfidence: 0.60 },
            fuzzyMatch: { weight: 0.5, minConfidence: 0.40 }
        };
        
        this.initialize();
    }

    /**
     * 初始化翻译器
     * @INIT 初始化酒店和机场数据索引
     */
    async initialize() {
        try {
            // 等待依赖数据加载
            await this.waitForDependencies();
            
            // 初始化酒店数据索引
            this.initializeHotelIndex();
            
            // 初始化机场数据
            this.initializeAirportData();
            
            this.initialized = true;
            console.log('✅ 地址翻译器初始化完成', {
                hotelCount: this.hotelData ? this.hotelData.length : 0,
                airportCount: this.airportData ? Object.keys(this.airportData.mappings).length : 0
            });
            
        } catch (error) {
            console.error('❌ 地址翻译器初始化失败:', error);
        }
    }

    /**
     * 等待依赖数据加载
     * @DEPENDENCY 确保酒店和机场数据已加载
     */
    async waitForDependencies() {
        const maxWaitTime = 5000; // 5秒超时
        const checkInterval = 100; // 100ms检查间隔
        let waitTime = 0;
        
        return new Promise((resolve, reject) => {
            const checkDependencies = () => {
                // 检查酒店数据
                if (window.OTA && window.OTA.hotelData) {
                    this.hotelData = window.OTA.hotelData;
                }
                
                // 检查机场数据
                if (window.AIRPORT_DATA) {
                    this.airportData = window.AIRPORT_DATA;
                }
                
                // 如果都加载完成
                if (this.hotelData && this.airportData) {
                    resolve();
                    return;
                }
                
                // 检查超时
                waitTime += checkInterval;
                if (waitTime >= maxWaitTime) {
                    reject(new Error('依赖数据加载超时'));
                    return;
                }
                
                // 继续等待
                setTimeout(checkDependencies, checkInterval);
            };
            
            checkDependencies();
        });
    }

    /**
     * 初始化酒店数据索引
     * @INIT 建立酒店名称的快速查找索引
     */
    initializeHotelIndex() {
        if (!this.hotelData) return;
        
        this.hotelIndex = {
            chinese: new Map(),
            english: new Map(),
            keywords: new Map()
        };
        
        this.hotelData.forEach((hotel, index) => {
            // 中文名称索引
            if (hotel.chinese_name) {
                this.hotelIndex.chinese.set(hotel.chinese_name.toLowerCase(), {
                    ...hotel,
                    index: index
                });
                
                // 提取关键词
                this.extractKeywords(hotel.chinese_name).forEach(keyword => {
                    if (!this.hotelIndex.keywords.has(keyword)) {
                        this.hotelIndex.keywords.set(keyword, []);
                    }
                    this.hotelIndex.keywords.get(keyword).push({
                        ...hotel,
                        index: index
                    });
                });
            }
            
            // 英文名称索引
            if (hotel.english_name) {
                this.hotelIndex.english.set(hotel.english_name.toLowerCase(), {
                    ...hotel,
                    index: index
                });
                
                // 提取关键词
                this.extractKeywords(hotel.english_name).forEach(keyword => {
                    if (!this.hotelIndex.keywords.has(keyword)) {
                        this.hotelIndex.keywords.set(keyword, []);
                    }
                    this.hotelIndex.keywords.get(keyword).push({
                        ...hotel,
                        index: index
                    });
                });
            }
        });
        
        console.log('📚 酒店索引建立完成', {
            chinese: this.hotelIndex.chinese.size,
            english: this.hotelIndex.english.size,
            keywords: this.hotelIndex.keywords.size
        });
    }

    /**
     * 初始化机场数据
     * @INIT 准备机场翻译数据
     */
    initializeAirportData() {
        // 机场数据已经在airport-data.js中准备好了
        console.log('✈️ 机场数据初始化完成');
    }

    /**
     * 提取关键词
     * @UTIL 从文本中提取有意义的关键词
     */
    extractKeywords(text) {
        if (!text) return [];
        
        // 移除常见的无意义词汇
        const stopWords = ['酒店', '宾馆', '旅馆', 'hotel', 'inn', 'resort', 'the', 'a', 'an'];
        
        // 分词（简单实现）
        const words = text.toLowerCase()
            .replace(/[^\w\u4e00-\u9fff\s]/g, ' ') // 保留中英文和数字
            .split(/\s+/)
            .filter(word => word.length >= 2 && !stopWords.includes(word));
        
        return [...new Set(words)]; // 去重
    }

    /**
     * 主要翻译接口
     * @SERVICE 提供地址翻译服务
     */
    async translateAddress(address, options = {}) {
        if (!this.initialized) {
            console.warn('⚠️ 地址翻译器未初始化完成');
            return this.createTranslationResult(address, null, 0, 'not_initialized');
        }
        
        if (!address || typeof address !== 'string') {
            return this.createTranslationResult(address, null, 0, 'invalid_input');
        }
        
        // 检查缓存
        const cacheKey = `${address}_${JSON.stringify(options)}`;
        if (this.translationCache.has(cacheKey)) {
            return this.translationCache.get(cacheKey);
        }
        
        try {
            console.log('🔄 开始地址翻译:', address);
            
            // 预处理和过滤
            if (!this.shouldTranslate(address)) {
                const result = this.createTranslationResult(address, null, 0, 'filtered_out');
                this.translationCache.set(cacheKey, result);
                return result;
            }
            
            // 尝试机场翻译
            const airportResult = await this.translateAirport(address);
            if (airportResult.confidence > 0.6) {
                this.translationCache.set(cacheKey, airportResult);
                return airportResult;
            }
            
            // 尝试酒店翻译
            const hotelResult = await this.translateHotel(address);
            if (hotelResult.confidence > 0.4) {
                this.translationCache.set(cacheKey, hotelResult);
                return hotelResult;
            }
            
            // 无法翻译
            const result = this.createTranslationResult(address, null, 0, 'no_match');
            this.translationCache.set(cacheKey, result);
            return result;
            
        } catch (error) {
            console.error('❌ 地址翻译失败:', error);
            return this.createTranslationResult(address, null, 0, 'translation_error', error.message);
        }
    }

    /**
     * 判断是否应该翻译
     * @UTIL 智能过滤不需要翻译的地址
     */
    shouldTranslate(address) {
        const cleanAddress = address.trim();
        
        // 长度检查
        if (cleanAddress.length < this.filterConfig.minLength || 
            cleanAddress.length > this.filterConfig.maxLength) {
            return false;
        }
        
        // 检查是否为纯英文（可能已经是翻译结果）
        if (/^[a-zA-Z0-9\s\-.,()]+$/.test(cleanAddress)) {
            return false;
        }
        
        // 检查通用地名
        const lowerAddress = cleanAddress.toLowerCase();
        for (const generic of this.filterConfig.genericLocations) {
            if (lowerAddress.includes(generic.toLowerCase())) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 机场地址翻译
     * @SERVICE 专门处理机场相关地址翻译
     */
    async translateAirport(address) {
        if (!this.airportData) {
            return this.createTranslationResult(address, null, 0, 'airport_data_unavailable');
        }

        // 检查是否包含机场关键词
        if (!this.airportData.containsAirportKeywords(address)) {
            return this.createTranslationResult(address, null, 0, 'not_airport');
        }

        console.log('✈️ 检测到机场相关地址:', address);

        // 尝试直接匹配机场名称
        const directMatch = this.findDirectAirportMatch(address);
        if (directMatch) {
            return this.createTranslationResult(
                address,
                directMatch.english_name,
                0.95,
                'airport_direct_match',
                null,
                {
                    source: 'airport_database',
                    matchType: 'direct',
                    airportInfo: directMatch
                }
            );
        }

        // 尝试城市名匹配
        const cityMatch = this.findAirportByCity(address);
        if (cityMatch) {
            return this.createTranslationResult(
                address,
                cityMatch.english_name,
                0.85,
                'airport_city_match',
                null,
                {
                    source: 'airport_database',
                    matchType: 'city',
                    airportInfo: cityMatch
                }
            );
        }

        return this.createTranslationResult(address, null, 0, 'airport_no_match');
    }

    /**
     * 直接匹配机场名称
     * @UTIL 在机场数据中查找直接匹配
     */
    findDirectAirportMatch(address) {
        const lowerAddress = address.toLowerCase();

        for (const [airportId, airportInfo] of Object.entries(this.airportData.mappings)) {
            // 检查中文名称
            for (const chineseName of airportInfo.chinese_names) {
                if (address.includes(chineseName)) {
                    return airportInfo;
                }
            }

            // 检查英文名称
            if (lowerAddress.includes(airportInfo.english_name.toLowerCase())) {
                return airportInfo;
            }

            // 检查IATA代码
            if (lowerAddress.includes(airportInfo.iata_code.toLowerCase())) {
                return airportInfo;
            }
        }

        return null;
    }

    /**
     * 根据城市名查找机场
     * @UTIL 通过城市名称推断机场
     */
    findAirportByCity(address) {
        for (const [cityName, airportId] of Object.entries(this.airportData.cityToAirport)) {
            if (address.includes(cityName)) {
                return this.airportData.mappings[airportId];
            }
        }
        return null;
    }

    /**
     * 酒店地址翻译
     * @SERVICE 基于酒店数据库的地址翻译
     */
    async translateHotel(address) {
        if (!this.hotelIndex) {
            return this.createTranslationResult(address, null, 0, 'hotel_index_unavailable');
        }

        console.log('🏨 尝试酒店地址翻译:', address);

        // 1. 完整匹配
        const exactMatch = this.findExactHotelMatch(address);
        if (exactMatch) {
            return this.createTranslationResult(
                address,
                exactMatch.hotel.english_name,
                0.95,
                'hotel_exact_match',
                null,
                {
                    source: 'hotel_database',
                    matchType: 'exact',
                    hotelInfo: exactMatch.hotel,
                    similarity: exactMatch.similarity
                }
            );
        }

        // 2. 高相似度匹配
        const similarMatch = this.findSimilarHotelMatch(address);
        if (similarMatch && similarMatch.similarity > 0.8) {
            return this.createTranslationResult(
                address,
                similarMatch.hotel.english_name,
                similarMatch.similarity,
                'hotel_similar_match',
                null,
                {
                    source: 'hotel_database',
                    matchType: 'similar',
                    hotelInfo: similarMatch.hotel,
                    similarity: similarMatch.similarity
                }
            );
        }

        // 3. 关键词匹配
        const keywordMatch = this.findKeywordHotelMatch(address);
        if (keywordMatch && keywordMatch.confidence > 0.6) {
            return this.createTranslationResult(
                address,
                keywordMatch.hotel.english_name,
                keywordMatch.confidence,
                'hotel_keyword_match',
                null,
                {
                    source: 'hotel_database',
                    matchType: 'keyword',
                    hotelInfo: keywordMatch.hotel,
                    matchedKeywords: keywordMatch.keywords
                }
            );
        }

        return this.createTranslationResult(address, null, 0, 'hotel_no_match');
    }

    /**
     * 完整匹配酒店名称
     * @UTIL 查找完全匹配的酒店名称
     */
    findExactHotelMatch(address) {
        const lowerAddress = address.toLowerCase();

        // 检查中文索引
        for (const [hotelName, hotelInfo] of this.hotelIndex.chinese) {
            if (lowerAddress.includes(hotelName) || address.includes(hotelInfo.chinese_name)) {
                return {
                    hotel: hotelInfo,
                    similarity: 1.0
                };
            }
        }

        // 检查英文索引
        for (const [hotelName, hotelInfo] of this.hotelIndex.english) {
            if (lowerAddress.includes(hotelName)) {
                return {
                    hotel: hotelInfo,
                    similarity: 1.0
                };
            }
        }

        return null;
    }

    /**
     * 相似度匹配酒店名称
     * @UTIL 使用编辑距离算法进行模糊匹配
     */
    findSimilarHotelMatch(address) {
        let bestMatch = null;
        let bestSimilarity = 0;

        // 检查中文酒店名
        for (const [hotelName, hotelInfo] of this.hotelIndex.chinese) {
            const similarity = this.calculateSimilarity(address, hotelInfo.chinese_name);
            if (similarity > bestSimilarity && similarity > 0.7) {
                bestSimilarity = similarity;
                bestMatch = {
                    hotel: hotelInfo,
                    similarity: similarity
                };
            }
        }

        return bestMatch;
    }

    /**
     * 关键词匹配酒店
     * @UTIL 基于关键词的酒店匹配
     */
    findKeywordHotelMatch(address) {
        const addressKeywords = this.extractKeywords(address);
        let bestMatch = null;
        let bestScore = 0;

        for (const keyword of addressKeywords) {
            if (this.hotelIndex.keywords.has(keyword)) {
                const hotels = this.hotelIndex.keywords.get(keyword);
                for (const hotel of hotels) {
                    const score = this.calculateKeywordScore(addressKeywords, hotel);
                    if (score > bestScore) {
                        bestScore = score;
                        bestMatch = {
                            hotel: hotel,
                            confidence: Math.min(score, 0.8), // 关键词匹配最高0.8
                            keywords: [keyword]
                        };
                    }
                }
            }
        }

        return bestMatch;
    }

    /**
     * 计算文本相似度
     * @UTIL 使用简化的编辑距离算法
     */
    calculateSimilarity(str1, str2) {
        if (!str1 || !str2) return 0;

        const len1 = str1.length;
        const len2 = str2.length;

        if (len1 === 0) return len2 === 0 ? 1 : 0;
        if (len2 === 0) return 0;

        // 简化的相似度计算
        const longer = len1 > len2 ? str1 : str2;
        const shorter = len1 > len2 ? str2 : str1;

        if (longer.includes(shorter)) {
            return shorter.length / longer.length;
        }

        // 计算公共子串
        let commonLength = 0;
        for (let i = 0; i < shorter.length; i++) {
            if (longer.includes(shorter[i])) {
                commonLength++;
            }
        }

        return commonLength / Math.max(len1, len2);
    }

    /**
     * 计算关键词匹配分数
     * @UTIL 基于关键词重叠度计算匹配分数
     */
    calculateKeywordScore(addressKeywords, hotel) {
        const hotelKeywords = [
            ...this.extractKeywords(hotel.chinese_name || ''),
            ...this.extractKeywords(hotel.english_name || '')
        ];

        let matchCount = 0;
        for (const keyword of addressKeywords) {
            if (hotelKeywords.includes(keyword)) {
                matchCount++;
            }
        }

        return matchCount / Math.max(addressKeywords.length, hotelKeywords.length);
    }

    /**
     * 创建翻译结果对象
     * @UTIL 标准化翻译结果格式
     */
    createTranslationResult(original, translated, confidence, status, error = null, metadata = {}) {
        return {
            original: original,
            translated: translated,
            confidence: confidence,
            status: status,
            timestamp: new Date().toISOString(),
            error: error,
            metadata: metadata
        };
    }
}

// 延迟初始化，确保依赖数据已加载
setTimeout(() => {
    if (!window.addressTranslator) {
        window.addressTranslator = new AddressTranslator();
        console.log('🌐 地址翻译器已创建');
    }
}, 1000);
